package com.ql.rent.service.trade;

import com.ql.rent.vo.PageListVo;
import com.ql.rent.dto.trade.TransferReportSummaryDTO;
import com.ql.rent.param.trade.TransferContractStatusParam;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusQuery;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.share.result.Result;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.trade.QueryIllegalTransferAuthVO;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;

import java.util.Date;
import java.util.List;

public interface ITransferContractStatusService {

    /**
     * 匹配转移合同，获取违章的转移状态
     * @param merchantId
     * @param vehicleId
     * @param illegalTime
     * @return
     */
    TransferContractStatusVo getTransferStatus(Long merchantId, Long vehicleId, Long orderId, Date illegalTime);

    /**
     * 按优先级匹配转移合同，优先匹配已上报和已终止的合同
     * @param merchantId 商家ID
     * @param vehicleId 车辆ID
     * @param illegalTime 违章时间
     * @return 匹配的合同状态和对应的订单ID
     */
    TransferContractStatusVo getTransferStatusByPriority(Long merchantId, Long vehicleId, Date illegalTime);

    /**
     * 保存违章转移合同状态表
     */
    Result<Boolean> saveTransferContractStatus(TransferContractStatusParam param);

    /**
     * 查询合同
     */
    Result<List<TransferContractStatusVo>> getTransferContractStatus(TransferContractStatusParam param);

    /**
     * 查询当前订单的最新可能成功或已经成功的合同
     */
    TransferContractStatusVo getLatestContract(Long merchantId, Long vehicleId, Long orderId);

    /**
     * 录入合同
     */
    Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime);

    /**
     * 终止合同 return true表示有终止合同
     * orderId 或 vehicleId不能为空
     * abortDate=null时，取当前时间
     * orderId != null && needSubmit=true,表示订单的合同可以补充
     */
    Boolean abortAllContract(Long orderId, Long vehicleId, Date abortDate, boolean needSubmit);

    /**
     * 拉取更新合同(定时任务)
     */
    void syncContractByJob();

    /**
     * 续期 录入合同(定时任务)
     */
    void submitDelayContractByJob();

    /**
     * 作废合同
     */
    Result<Boolean> cancelContract(String contractId);

    Result<Boolean> submitContract(Long orderId, Date beginTime, Date endTime, Long vehicleId);

    /**
     * 测试上报公众号消息推送
     */
    void testPushTransferMsg(Long orderId, String code, Date beginTime, Date endTime, String account);

    Result<Boolean> authorizeIllegal(Long orderId, LoginVo loginVo);

    Result<QueryIllegalTransferAuthVO> queryIllegalTransferAuth(Long orderId, Long merchantId);

    /**
     * if (是有效违章转移订单的车辆 && 订单状态在已排车 && 订单的身份证错误)
     *   异步发送订单身份证不正确企业微信消息
     */
    void sendOrderIdCardErrorMsg(Long orderId);

    /**
     * 统计转移上报情况(定时任务)
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 统计结果
     */
    TransferReportSummaryDTO getTransferReportSummary(Date startTime, Date endTime);

    /**
     * 执行转移上报统计任务并发送企业微信消息
     */
    void executeTransferReportTask();

    /**
     * 执行违章月度账单推送任务
     */
    void executeViolationMonthlyBillTask();

    /**
     * 违章登录成功后自动重试失败的合同
     * @param account 122账号
     */
    void retryFailedContractsOnLoginSuccess(String account);

    /**
     * 分页查询合同列表
     * @param query 查询条件
     * @return 分页结果
     */
    Result<PageListVo<TransferContractStatusVo>> getContractList(TransferContractStatusQuery query);

    /**
     * 手动上报合同
     * @param request 上报请求
     * @param merchantId 商家ID
     * @param userId 操作用户ID
     * @return 上报结果
     */
    Result<com.ql.rent.vo.trade.SubmitContractResponse> manualSubmitContract(SubmitContractRequest request, Long merchantId, Long userId);
}
