# 取车

## OpenAPI Specification

```yaml
openapi: 3.0.1
info:
  title: ''
  description: ''
  version: 1.0.0
paths:
  /api/order/vehicle/pick_return/pick_up/v1:
    post:
      summary: 取车
      deprecated: false
      description: ''
      operationId: doPickUpUsingPOST_1
      tags:
        - 取/还车相关
        - 取/还车相关
      parameters: []
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/%E5%8F%96%E8%BD%A6%E5%8F%82%E6%95%B0'
      responses:
        '200':
          description: OK
          content:
            '*/*':
              schema:
                $ref: >-
                  #/components/schemas/%E8%BF%94%E5%9B%9E%E5%AE%9E%E4%BD%93%C2%ABint%C2%BB
          headers: {}
          x-apifox-name: 成功
        '201':
          description: Created
          headers: {}
          x-apifox-name: 成功
        '401':
          description: Unauthorized
          headers: {}
          x-apifox-name: 没有权限
        '403':
          description: Forbidden
          headers: {}
          x-apifox-name: 禁止访问
        '404':
          description: Not Found
          headers: {}
          x-apifox-name: 记录不存在
      security: []
      x-apifox-folder: 取/还车相关
      x-apifox-status: released
      x-run-in-apifox: https://app.apifox.com/web/project/6753502/apis/api-320858364-run
components:
  schemas:
    取车参数:
      title: 取车参数
      required:
        - attList
        - contractSign
        - pickupVehicleId
      type: object
      properties:
        addServiceIdList:
          type: array
          description: 新增的附加产品id
          items:
            type: integer
            format: int64
        attList:
          type: array
          description: 附件列表
          items: &ref_0
            $ref: >-
              #/components/schemas/%E5%8F%96%E6%8D%A2%E8%BD%A6%E9%99%84%E4%BB%B6VO
        contract:
          description: 合同相关参数
          $ref: '#/components/schemas/PickReturnContractParam'
        contractSign:
          type: string
          description: 电子合同是否盖章 电子合同验车信息接口使用.首次传0，二次传1
          format: byte
        damageOrder:
          description: ' 还车时 车损记录'
          $ref: '#/components/schemas/%E8%BD%A6%E6%8D%9F%E8%AE%B0%E5%BD%95VO'
        deductionPayType:
          type: string
          description: 扣费方式:还车时用到;取车，该字段为空; 1线上（平台代扣） 2线下
          format: byte
        deposit:
          description: 押金相关参数, 押金未支付的场景需要传入
          $ref: '#/components/schemas/DepositParam'
        illegalOrder:
          description: '还车时 违章记录 '
          $ref: '#/components/schemas/VehicleIllegalOrderParam'
        inspection:
          description: 验车单数据
          $ref: '#/components/schemas/%E9%AA%8C%E8%BD%A6%E5%8D%95%E5%AF%B9%E8%B1%A1'
        insuranceServiceIdList:
          type: array
          description: 新增的保险产品id
          items:
            type: integer
            format: int64
        maxOilLiter:
          type: integer
          description: 最大油量
          format: int32
        mileage:
          type: integer
          description: 里程
          format: int32
        mileageAtt: *ref_0
        oilLiter:
          type: integer
          description: 油升数
          format: int32
        openThird:
          type: boolean
        orderId:
          type: integer
          description: 订单id
          format: int64
        payKind:
          type: string
          format: byte
        prTime:
          type: string
          description: 取/还车时间
          format: date-time
        refundPayType:
          type: string
          description: 退费方式:还车时用到;取车，该字段为空1线上（平台代收） 2线下
          format: byte
        returnDeductionItemList:
          type: array
          description: 还车时 扣费明细
          items: &ref_1
            $ref: >-
              #/components/schemas/%E8%BF%98%E8%BD%A6%E6%94%B6%E9%80%80%E8%B4%B9%E6%98%8E%E7%BB%86VO
        returnRefundItemList:
          type: array
          description: 还车时 退费明细
          items: *ref_1
        wukongExtraAddList:
          type: array
          description: 还车时 悟空补款明细
          items:
            $ref: '#/components/schemas/AddPayDetailDTO'
        wukongPayType:
          type: string
          description: 还车时 悟空补款方式:(OFFLINE:线下支付,ONLINE:线上支付)
        pickupVehicleId:
          type: string
          description: 取车车辆id
      x-apifox-orders:
        - addServiceIdList
        - attList
        - contract
        - contractSign
        - damageOrder
        - deductionPayType
        - deposit
        - illegalOrder
        - inspection
        - insuranceServiceIdList
        - maxOilLiter
        - mileage
        - mileageAtt
        - oilLiter
        - openThird
        - orderId
        - payKind
        - prTime
        - refundPayType
        - returnDeductionItemList
        - returnRefundItemList
        - wukongExtraAddList
        - wukongPayType
        - pickupVehicleId
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    AddPayDetailDTO:
      title: AddPayDetailDTO
      type: object
      properties:
        attachmentList:
          type: array
          items:
            type: string
        comment:
          type: string
        itemAmount:
          type: integer
          format: int32
        itemType:
          type: string
        name:
          type: string
      x-apifox-orders:
        - attachmentList
        - comment
        - itemAmount
        - itemType
        - name
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    还车收退费明细VO:
      title: 还车收退费明细VO
      type: object
      properties:
        attUrlList:
          type: array
          description: 附件列表
          items:
            type: string
        createTime:
          type: integer
          description: 创建时间
          format: int64
        expenseAmount:
          type: integer
          description: 费用金额
          format: int64
        expenseItemPropId:
          type: integer
          description: 收/退费项id
          format: int64
        id:
          type: integer
          description: 主键id
          format: int64
        itemName:
          type: string
          description: 项目名称
        itemType:
          type: string
          description: 类型1收费 2退费
          format: byte
        payKind:
          type: string
          format: byte
        payNo:
          type: string
        payStatus:
          type: string
          format: byte
        refundPayStatus:
          type: string
          format: byte
        refundPayType:
          type: string
          description: 支付方式 1线上 2线下
          format: byte
        returnPaid:
          type: boolean
        vehicleReturnId:
          type: integer
          description: 还车信息id
          format: int64
      x-apifox-orders:
        - attUrlList
        - createTime
        - expenseAmount
        - expenseItemPropId
        - id
        - itemName
        - itemType
        - payKind
        - payNo
        - payStatus
        - refundPayStatus
        - refundPayType
        - returnPaid
        - vehicleReturnId
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    验车单对象:
      title: 验车单对象
      type: object
      properties:
        appearance:
          description: 车辆外观验车结果
          $ref: >-
            #/components/schemas/%E5%8F%96%E8%BF%98%E8%BD%A6-%E9%AA%8C%E8%BD%A6%E5%8D%95-%E8%BD%A6%E8%BE%86%E5%A4%96%E8%A7%82
        interiorList:
          type: array
          description: 固定物件验车结果
          items:
            $ref: >-
              #/components/schemas/%E5%8F%96%E8%BF%98%E8%BD%A6-%E9%AA%8C%E8%BD%A6%E5%8D%95%20%E5%9B%BA%E5%AE%9A%E7%89%A9%E4%BB%B6
        prType:
          type: string
          description: 取还车类型 0取车 1还车 10:商家送车验车 11:客人取车 12:行中换车 13:客人还车
          format: byte
      x-apifox-orders:
        - appearance
        - interiorList
        - prType
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    取还车-验车单 固定物件:
      title: 取还车-验车单 固定物件
      type: object
      properties:
        damaged:
          type: string
          description: 是否损坏 0:否 1是
          format: byte
        inspectionId:
          type: string
          description: 固定物件id
          format: byte
        name:
          type: string
          description: 固定物件名称
        prType:
          type: string
          description: 取还车类型 0取车 1还车
          format: byte
      x-apifox-orders:
        - damaged
        - inspectionId
        - name
        - prType
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    取还车-验车单-车辆外观:
      title: 取还车-验车单-车辆外观
      type: object
      properties:
        appearanceList:
          type: array
          description: 有损坏的车辆外观位置
          items:
            $ref: >-
              #/components/schemas/%E5%8F%96%E8%BF%98%E8%BD%A6-%E9%AA%8C%E8%BD%A6%E5%8D%95-
        prType:
          type: string
          description: 取还车类型 0取车 1还车
          format: byte
        remark:
          type: string
          description: 取车时 备注
      x-apifox-orders:
        - appearanceList
        - prType
        - remark
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    取还车-验车单-:
      title: 取还车-验车单-
      type: object
      properties:
        appearanceName:
          type: string
          description: 外观名称
        appearanceNo:
          type: string
          description: 外观编号
          format: byte
        damaged:
          type: string
          description: 是否损坏
          format: byte
      x-apifox-orders:
        - appearanceName
        - appearanceNo
        - damaged
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    VehicleIllegalOrderParam:
      title: VehicleIllegalOrderParam
      required:
        - illegalTime
        - proofList
      type: object
      properties:
        agencyFee:
          type: integer
          description: 代办费用 二期新增
          format: int64
        contractDamageAmount:
          type: integer
          description: 违约金额（总扣费金额），二期内容删除该字段
          format: int64
        deductionStatus:
          type: string
          description: 扣款状态:1失败 2扣款成功 3已退款
          format: byte
        deductionType:
          type: string
          description: 扣款类型 1平台代扣 2线下
          format: byte
        fraction:
          type: integer
          description: 违章扣分
          format: int32
        handleDriverName:
          type: string
          description: 处理人名称
        handleDriverlicNo:
          type: string
          description: 处理证件号
        handleStatus:
          type: string
          description: 处理状态(0-待处理,1-已处理,2-无需处理)
          format: byte
        id:
          type: integer
          description: 违章记录主键id
          format: int64
        illegalAction:
          type: string
          description: 违章行为
        illegalAddr:
          type: string
          description: 违章地点
        illegalCityId:
          type: integer
          description: 违章城市id
          format: int64
        illegalExtraDTO:
          $ref: '#/components/schemas/IllegalExtraDTO'
        illegalTime:
          type: string
          description: 违章时间
          format: date-time
        illegalType:
          type: string
          description: 违章类型(0-现场单,1-非现场单) 二期新增
          format: byte
        orderId:
          type: integer
          description: 订单id
          format: int64
        penaltyAmount:
          type: integer
          description: 违章罚款
          format: int64
        processorType:
          type: string
          format: byte
        proofList:
          type: array
          description: 违章记录凭证相关
          items:
            $ref: >-
              #/components/schemas/%E8%BF%9D%E7%AB%A0%E5%8D%95%E5%87%AD%E8%AF%81VO
        realHandleStatus:
          type: string
          description: 交管违章处理状态 -1未知 0表示未处理，1表示已处理，2已消网, 3已处理未缴费。
          format: byte
        remark:
          type: string
          description: 备注
        transferStatus:
          type: string
          description: 违章转移状态 -3未知 -2计划上报 -1表示上报失败 0表示上报中，1表示已上报，2已终止, 3已取消。
          format: byte
        vehicleId:
          type: integer
          description: 车辆id, 二期新增。为null时，根据订单车辆创建违章
          format: int64
      description: 违章记录创建参数
      x-apifox-orders:
        - agencyFee
        - contractDamageAmount
        - deductionStatus
        - deductionType
        - fraction
        - handleDriverName
        - handleDriverlicNo
        - handleStatus
        - id
        - illegalAction
        - illegalAddr
        - illegalCityId
        - illegalExtraDTO
        - illegalTime
        - illegalType
        - orderId
        - penaltyAmount
        - processorType
        - proofList
        - realHandleStatus
        - remark
        - transferStatus
        - vehicleId
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    违章单凭证VO:
      title: 违章单凭证VO
      type: object
      properties:
        id:
          type: integer
          description: 主键id（修改时传参带入。如果id为空，表示是此次修改新增的图片；否则表示是之前已有的图片）
          format: int64
        proofUrl:
          type: string
          description: 凭证附件全地址
      x-apifox-orders:
        - id
        - proofUrl
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    IllegalExtraDTO:
      title: IllegalExtraDTO
      type: object
      properties:
        note:
          type: string
        originalFraction:
          type: string
        originalPenaltyAmount:
          type: string
      x-apifox-orders:
        - note
        - originalFraction
        - originalPenaltyAmount
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    DepositParam:
      title: DepositParam
      type: object
      properties:
        chargeProofUrl:
          type: string
          description: 附件url 目前做了两个url字段平铺
        chargeProofUrl2:
          type: string
          description: 附件url2
        illegalDepositAmount:
          type: integer
          format: int32
        payType:
          type: string
          description: 支付方式
          format: byte
        remark:
          type: string
          description: 备注枚举值
          format: byte
        remarkDesc:
          type: string
          description: 备注说明
        vehicleDepositAmount:
          type: integer
          format: int32
      x-apifox-orders:
        - chargeProofUrl
        - chargeProofUrl2
        - illegalDepositAmount
        - payType
        - remark
        - remarkDesc
        - vehicleDepositAmount
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    车损记录VO:
      title: 车损记录VO
      required:
        - damageProofList
        - depreciationFee
        - outageFee
        - repairFee
      type: object
      properties:
        damageProofList:
          type: array
          description: 凭证url
          items:
            $ref: '#/components/schemas/%E8%BD%A6%E6%8D%9F%E5%87%AD%E8%AF%81VO'
        damageTime:
          type: string
          description: 车损时间
          format: date-time
        deductionAmount:
          type: integer
          description: 实扣金额
          format: int32
        deductionStatus:
          type: string
          description: 扣款状态:1失败 2扣款成功 3已退款
          format: byte
        deductionType:
          type: string
          description: 扣款方式 1平台代扣 2线下
          format: byte
        depreciationFee:
          type: integer
          description: 折旧费
          format: int64
        id:
          type: integer
          description: 主键id
          format: int64
        orderId:
          type: integer
          description: 订单id
          format: int64
        otherFee:
          type: integer
          description: 其他费用
          format: int64
        outageFee:
          type: integer
          description: 停运费用
          format: int64
        refundAmount:
          type: integer
          description: 退款金额
          format: int64
        repairFee:
          type: integer
          description: 维修费用
          format: int64
        source:
          type: string
          description: 来源 1:线下渠道; 2:ctrip; 3:feizhu;4:hello; 5:zuzuche; 6:wukong
          format: byte
      x-apifox-orders:
        - damageProofList
        - damageTime
        - deductionAmount
        - deductionStatus
        - deductionType
        - depreciationFee
        - id
        - orderId
        - otherFee
        - outageFee
        - refundAmount
        - repairFee
        - source
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    车损凭证VO:
      title: 车损凭证VO
      type: object
      properties:
        damageOrderId:
          type: integer
          description: 车损单id
          format: int64
        id:
          type: integer
          description: 主键id（修改时传参带入。如果id为空，表示是此次修改新增的图片；否则表示是之前已有的图片
          format: int64
        proofUrl:
          type: string
          description: 凭证附件全地址
      x-apifox-orders:
        - damageOrderId
        - id
        - proofUrl
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    PickReturnContractParam:
      title: PickReturnContractParam
      type: object
      properties:
        contractUrl:
          type: string
          description: 签署合同url
        inspectionUrl:
          type: string
          description: 验车单图片url
      x-apifox-orders:
        - contractUrl
        - inspectionUrl
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    取换车附件VO:
      title: 取换车附件VO
      required:
        - attType
        - attUrl
      type: object
      properties:
        attType:
          type: string
          description: 附件类型 0图片， 1视频
          format: byte
        attUrl:
          type: string
          description: 附件url
      x-apifox-orders:
        - attType
        - attUrl
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
    返回实体«int»:
      title: 返回实体«int»
      type: object
      properties:
        code:
          type: integer
          description: 响应码
          format: int32
        data:
          type: integer
          description: 返回实体，请求处理成功时返回
          format: int32
        errorCode:
          type: string
          description: 错误码，接口处理失败时返回
        i18nCode:
          type: string
          description: 国际化标识
        message:
          type: string
          description: 错误信息,异常时返回
        success:
          type: boolean
        tip:
          type: string
      x-apifox-orders:
        - code
        - data
        - errorCode
        - i18nCode
        - message
        - success
        - tip
      x-apifox-ignore-properties: []
      x-apifox-folder: ''
  securitySchemes: {}
servers: []
security: []

```