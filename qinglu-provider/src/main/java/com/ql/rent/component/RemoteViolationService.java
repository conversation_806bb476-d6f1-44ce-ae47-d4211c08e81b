package com.ql.rent.component;

import com.alibaba.fastjson2.JSON;
import com.ql.enums.open.ResultCodeEnum;
import com.ql.rent.remote.vehicle.dto.VehicleIllegalRecordDTO;
import com.ql.rent.remote.vehicle.vo.VehicleResponse;
import com.ql.rent.remote.vehicle.vo.request.*;
import com.ql.rent.remote.vehicle.vo.response.*;
import com.ql.rent.service.common.OkHttpService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import lombok.extern.slf4j.Slf4j;
import okhttp3.*;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.security.MessageDigest;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 上智广链企业查违章等服务
 */
@Service
@Slf4j
public class RemoteViolationService {
    @Resource
    private OkHttpService okHttpService;

    /**
     * appId
     */
    @Value("${vehicle.appId}")
    public String appId;
    /**
     * secretKey
     */
    @Value("${vehicle.secretKey}")
    public String secretKey;
    /**
     * url
     */
    @Value("${vehicle.url}")
    public String url;

    /**
     * 违章查询
     * @param license
     * @param carType
     * @return
     */
    public Result<VehicleIllegalResponse> getVehicleIllegal(String license, String carType) {
        Long timespan = System.currentTimeMillis();
        //请求参数，设置按键排序
        Map<String, Object> requestMap = new TreeMap<>(String::compareTo);
        requestMap.put("carType", carType);
        requestMap.put("carNumber", license);
        //组装加密sign
        List<String> list = new ArrayList<>();
        for (Map.Entry<String, Object> entry : requestMap.entrySet()) {
            list.add((String) entry.getValue());
        }
        String sortedStr = StringUtils.join(list, "");
        String sign = MD5Util.MD5(timespan.toString().concat(sortedStr).concat(secretKey));
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url + "/cxy/enterprise/ViolationQuery")).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 创建调用
        log.info("违章查询,license={},carType={}", license, carType);
        String response = okHttpService.okPostJson(urlBuilder.build().toString(), null, requestMap);
        if (response == null) {
            return ResultUtil.failResult("remote违章查询失败");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse) && vehicleResponse.getData() != null) {
            VehicleIllegalResponse vehicleIllegalResponse = JSON.parseObject(JSON.toJSONString(vehicleResponse.getData()), VehicleIllegalResponse.class);
            if (vehicleIllegalResponse != null && CollectionUtils.isNotEmpty(vehicleIllegalResponse.getRecords())) {
                for (VehicleIllegalRecordDTO record : vehicleIllegalResponse.getRecords()) {
                    // 三方违章的接口-1才表示已消网
                    if (record.getStatus().equals("-1")) {
                        record.setStatus("2");
                    }
                }
            }
            return ResultUtil.successResult(vehicleIllegalResponse);
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 录入合同
     * @param submitContractReq
     * @return
     */
    public Result<SubmitContractResponse> submitContract(SubmitContractReq submitContractReq) {
        if (submitContractReq == null) {
            return ResultUtil.failResult("参数错误");
        }

        // 去掉手机号
        submitContractReq.setDriverPhone(null);
        log.info("违章转移 录入合同, submitContractReq={}", JSON.toJSONString(submitContractReq));
        String response = postToRemoteService(submitContractReq, "/rent/contract/submit");
        if (response == null) {
            return ResultUtil.failResult(ResultCodeEnum.ViolationCodeEnum.open_violation_999.getCode(),"网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (vehicleResponse != null && vehicleResponse.getData() != null) {
            SubmitContractResponse submitContractResponse = JSON.parseObject(JSON.toJSONString(vehicleResponse.getData()), SubmitContractResponse.class);
            if (VehicleResponse.isSuccess(vehicleResponse) &&
                    (submitContractResponse.getPostCode() == null || "0".equals(submitContractResponse.getPostCode()))) {
                submitContractResponse.setPostCode("80000");
                submitContractResponse.setPostMsg("请求成功");
            }
            return ResultUtil.successResult(submitContractResponse);
        } else {
            if (vehicleResponse != null && vehicleResponse.getMsg() != null) {
                return ResultUtil.failResult(vehicleResponse.getCode().toString(), vehicleResponse.getMsg());
            }
            return ResultUtil.failResult("系统异常");
        }
    }

    /**
     * 合同作废
     * @param cancelContractReq
     * @return
     */
    public Result<Boolean> cancelContract(CancelContractReq cancelContractReq) {
        if (cancelContractReq == null || cancelContractReq.getOrderId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        String response = postToRemoteService(cancelContractReq, "/rent/contract/cancel");
        if (response == null) {
            return ResultUtil.failResult("网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse)) {
            return ResultUtil.successResult(true);
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 合同详情
     * @param contractDetailReq
     * @return
     */
    public Result<ContractDetailResponse> contractDetail(ContractDetailReq contractDetailReq) {
        if (contractDetailReq == null || contractDetailReq.getOrderId() == null) {
            return ResultUtil.failResult("参数错误");
        }

        String response = postToRemoteService(contractDetailReq, "/rent/contract/detail");
        if (response == null) {
            return ResultUtil.failResult("网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse) && vehicleResponse.getData() != null) {
            return ResultUtil.successResult(JSON.parseObject(JSON.toJSONString(vehicleResponse.getData()), ContractDetailResponse.class));
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 租赁合同列表
     * @param contractListReq
     * @return
     */
    public Result<List<ContractListResponse>> contractList(ContractListReq contractListReq) {
        if (contractListReq == null) {
            return ResultUtil.failResult("参数错误");
        }

        String response = postToRemoteService(contractListReq, "/rent/contract/list");
        if (response == null) {
            return ResultUtil.failResult("网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse) && vehicleResponse.getData() != null) {
            return ResultUtil.successResult(JSON.parseArray(JSON.toJSONString(vehicleResponse.getData()), ContractListResponse.class));
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 合同终止
     * @param abortContractReq
     * @return
     */
    public Result<Boolean> abortContract(AbortContractReq abortContractReq) {
        if (abortContractReq == null || abortContractReq.getOrderId() == null) {
            return ResultUtil.failResult("参数错误");
        }
        log.info("违章转移 合同终止, abortContractReq={}", JSON.toJSONString(abortContractReq));
        String response = postToRemoteService(abortContractReq, "/rent/contract/abort");
        if (response == null) {
            return ResultUtil.failResult("网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse)) {
            return ResultUtil.successResult(true);
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 强制转移
     * @param transferContractReq
     * @return
     */
    public Result<Boolean> transferContract(TransferContractReq transferContractReq) {
        if (transferContractReq == null || transferContractReq.getOrderId() == null || transferContractReq.getContractFile() == null) {
            return ResultUtil.failResult("参数错误");
        }
        log.info("违章转移 强制转移, transferContractReq={}", JSON.toJSONString(transferContractReq));
        String response = postToRemoteService(transferContractReq, "/rent/contract/transfer");
        if (response == null) {
            return ResultUtil.failResult("网络异常");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse)) {
            return ResultUtil.successResult(true);
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 拉取合同更新数据
     * @param dbVersion
     * @return
     */
    public Result<List<SyncContractResponse>> syncContract(Long dbVersion) {
        if (dbVersion == null) {
            return ResultUtil.failResult("参数错误");
        }

        Long timespan = System.currentTimeMillis();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("dbVersion", dbVersion);
        //组装加密sign
        String sign = MD5Util.MD5(timespan.toString().concat(JSON.toJSONString(requestMap)).concat(secretKey));
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url + "/rent/contract/syncdata")).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 创建调用
        String response = okHttpService.okPostJson(urlBuilder.build().toString(), null, requestMap);
        log.info("违章转移 同步合同更新数据, requestMap={}, response={}", JSON.toJSONString(requestMap), response);
        if (response == null) {
            return ResultUtil.failResult("remote违章转移,同步合同更新数据");
        }
        VehicleResponse vehicleResponse = JSON.parseObject(response, VehicleResponse.class);
        if (VehicleResponse.isSuccess(vehicleResponse)) {
            return ResultUtil.successResult(JSON.parseArray(JSON.toJSONString(vehicleResponse.getData()), SyncContractResponse.class));
        } else {
            return ResultUtil.failResult(vehicleResponse.getMsg());
        }
    }

    /**
     * 获取账号登陆二维码
     * @param loginQrCodeReq
     * @return
     */
    @WithSpan("[remote]获取账号登陆二维码")
    public Result<LoginQrCodeResponse> getLoginQrCode(LoginQrCodeReq loginQrCodeReq) {
        if (loginQrCodeReq == null || loginQrCodeReq.getUserName() == null) {
            return ResultUtil.failResult("参数错误");
        }
        Span span = Span.current();

        Long timespan = System.currentTimeMillis();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("userName", loginQrCodeReq.getUserName());
        //组装加密sign
        String sign = MD5Util.MD5(timespan.toString().concat(JSON.toJSONString(requestMap)).concat(secretKey));
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url + "/biz/enterprise/getloginqrcode")).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 创建调用
        String url = urlBuilder.build().toString();
        SpanEnhancer spanEnhancer = SpanEnhancer.of(span);
        spanEnhancer.withString("url", url).withJson("request", requestMap);
        String response = okHttpService.okPostJson(url, null, requestMap);
        spanEnhancer.withString("response", response);
        if (response == null) {
            return ResultUtil.failResult("remote违章,获取账号登陆二维码失败");
        }
        return ResultUtil.successResult(JSON.parseObject(response, LoginQrCodeResponse.class));
    }

    /**
     * 账号列表查询
     * @param getAccountListReq
     * @return
     */
    @WithSpan("[remote]账号列表查询")
    public Result<GetAccountListResponse> getAccountList(GetAccountListReq getAccountListReq) {
        if (getAccountListReq == null || getAccountListReq.getPartnerAccount() == null) {
            return ResultUtil.failResult("合作商账号不能为空");
        }
        Span span = Span.current();

        Long timespan = System.currentTimeMillis();
        Map<String, Object> requestMap = new HashMap<>();
        requestMap.put("PartnerAccount", getAccountListReq.getPartnerAccount());
        if (getAccountListReq.getUserNames() != null) {
            requestMap.put("UserNames", getAccountListReq.getUserNames());
        }
        if (getAccountListReq.getIsLogin() != null) {
            requestMap.put("IsLogin", getAccountListReq.getIsLogin());
        }
        
        //组装加密sign (使用查询类加密算法)
        String sign = MD5Util.MD5(timespan.toString().concat(JSON.toJSONString(requestMap)).concat(secretKey));
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url + "/biz/enterprise/GetAccountList")).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 创建调用
        String requestUrl = urlBuilder.build().toString();
        SpanEnhancer spanEnhancer = SpanEnhancer.of(span);
        spanEnhancer.withString("url", requestUrl).withJson("request", requestMap);
        String response = okHttpService.okPostJson(requestUrl, null, requestMap);
        spanEnhancer.withString("response", response);
        log.info("账号列表查询, requestMap={}, response={}", JSON.toJSONString(requestMap), response);
        if (response == null) {
            return ResultUtil.failResult("remote账号列表查询失败");
        }
        
        GetAccountListResponse accountListResponse = JSON.parseObject(response, GetAccountListResponse.class);
        if (accountListResponse != null && accountListResponse.getCode() != null && accountListResponse.getCode() == 0) {
            return ResultUtil.successResult(accountListResponse);
        } else {
            String errorMsg = accountListResponse != null && accountListResponse.getMsg() != null ? 
                accountListResponse.getMsg() : "账号列表查询失败";
            return ResultUtil.failResult(errorMsg);
        }
    }

    private String postToRemoteService(Object req, String suffixUrl) {
        Long timespan = System.currentTimeMillis();
        Map<String, Object> requestMap = convert2Map(req);
        //组装加密sign
        String signStr = timespan.toString().concat(JSON.toJSONString(requestMap)).concat(secretKey);
        String sign = MD5Util.MD5(signStr);
        // 构建URL参数
        HttpUrl.Builder urlBuilder = Objects.requireNonNull(HttpUrl.parse(url + suffixUrl)).newBuilder();
        urlBuilder.addQueryParameter("appid", appId);
        urlBuilder.addQueryParameter("timespan", String.valueOf(timespan));
        urlBuilder.addQueryParameter("sign", sign);
        urlBuilder.addQueryParameter("version", String.valueOf(3));
        // 创建调用
        String response = okHttpService.okPostJsonByLongReadTime(urlBuilder.build().toString(), null, requestMap);
        return response;
    }

    private <T> Map<String, Object> convert2Map(T request) {
        try {
            Map<String, Object> requestMap = new TreeMap<>(String::compareTo);
            Class<?> clazz = request.getClass();
            Field[] fields = clazz.getDeclaredFields();

            for (Field field : fields) {
                field.setAccessible(true);
                if (field.get(request) instanceof Date) {
                    // 使用 SimpleDateFormat 类将 Date 对象格式化为字符串形式
                    SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX");
                    String formattedDate = sdf.format(field.get(request));
                    requestMap.put(field.getName(), formattedDate);
                } else {
                    requestMap.put(field.getName(), field.get(request));
                }
            }

            return requestMap;
        } catch (IllegalAccessException e) {
            log.error("map转换异常, request={}", JSON.toJSONString(request), e);
            throw new RuntimeException(e);
        }
    }

    private static class MD5Util {

        public MD5Util() {
        }
        public static final String MD5(String s) {
            char[] hexDigits = new char[]{'0', '1', '2', '3', '4', '5', '6', '7', '8', '9', 'A', 'B', 'C', 'D', 'E', 'F'};
            try {
                byte[] btInput = s.getBytes("UTF-8");
                MessageDigest mdInst = MessageDigest.getInstance("MD5");
                mdInst.update(btInput);
                byte[] md = mdInst.digest();
                int j = md.length;
                char[] str = new char[j * 2];
                int k = 0;

                for(int i = 0; i < j; ++i) {
                    byte byte0 = md[i];
                    str[k++] = hexDigits[byte0 >>> 4 & 15];
                    str[k++] = hexDigits[byte0 & 15];
                }

                return new String(str);
            } catch (Exception var10) {
                var10.printStackTrace();
                return null;
            }
        }
    }
}

