package com.ql.rent.provider.trade;

import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.enums.open.ResultCodeEnum;
import com.ql.rent.component.RemoteViolationService;
import com.ql.rent.constant.IllegalTransferMessageTemplate;
import com.ql.rent.constant.MallServiceItemConstant;
import com.ql.rent.constant.QyWechatConstant;
import com.ql.rent.dao.trade.MallServiceOrderInfoMapperEx;
import com.ql.rent.enums.trade.MallOrderItemEnum;
import com.ql.rent.enums.trade.TransferContractBizTypeEnum;
import com.ql.rent.enums.trade.TransferContractStatusEnum;
import com.ql.rent.param.trade.TransferContractStatusParam;
import com.ql.rent.param.trade.TransferVehicleBindQuery;
import com.ql.rent.param.trade.MallServiceOrderParam;
import com.ql.rent.remote.vehicle.vo.request.*;
import com.ql.rent.remote.vehicle.vo.response.*;
import com.ql.rent.service.merchant.TrafficManagementBindService;
import com.ql.rent.service.third.IEnterpriseWechatService;
import com.ql.rent.service.trade.IThirdIllegalTransferService;
import com.ql.rent.service.trade.IMallServiceOrderInfoService;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.service.trade.IVehicleIllegalTransferDetailService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.merchant.TrafficManagementBindDTO;
import com.ql.rent.vo.trade.MallServiceOrderInfoDTO;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleIllegalTransferDetailVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.Date;
import java.util.List;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

/**
 * 违章转移第三方服务实现
 */
@Service
@Slf4j
public class ThirdIllegalTransferServiceImpl implements IThirdIllegalTransferService {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(ThirdIllegalTransferServiceImpl.class);

    @Resource
    private IVehicleIllegalTransferDetailService vehicleIllegalTransferDetailService;

    @Resource
    private IMallServiceOrderInfoService mallServiceOrderInfoService;

    @Resource
    private RemoteViolationService remoteViolationService;

    @Resource
    private IVehicleInfoService vehicleInfoService;

    @Resource
    private TrafficManagementBindService trafficManagementBindService;

    @Resource
    private MallServiceOrderInfoMapperEx mallServiceOrderInfoMapperEx;

    @Resource
    private ITransferContractStatusService transferContractStatusService;

    @Resource
    private IEnterpriseWechatService enterpriseWechatService;

    @Resource
    private Executor asyncPromiseExecutor;

    @Override
    public Result<List<VehicleIllegalTransferDetailVo>> listTransferVehicleBind(TransferVehicleBindQuery query) {
        logger.startLog().with("query", query).log("查询车辆绑定的订单");
        return vehicleIllegalTransferDetailService.listTransferVehicleBind(query);
    }

    @Override
    public Result<SubmitContractResponse> submitContract(SubmitContractReq submitContractReq, Long merchantId) {
        logger.startLog().with("submitContractReq", submitContractReq).with("merchantId", merchantId).logAudit("录入合同");
        
        // 参数验证
        if (merchantId == null) {
            return ResultUtil.failResult("商户ID不能为空");
        }
        if (submitContractReq == null || submitContractReq.getCarNumber() == null || submitContractReq.getDriverId() == null
                || submitContractReq.getContractNo() == null || submitContractReq.getRentType() == null
                || submitContractReq.getBeginTime() == null || submitContractReq.getEndTime() == null
                || submitContractReq.getCarType() == null) {
            return ResultUtil.failResult("参数错误");
        }
        
        // contractNo重复性校验
        TransferContractStatusParam contractNoQuery = new TransferContractStatusParam();
        contractNoQuery.setMerchantId(merchantId);
        contractNoQuery.setContractNo(submitContractReq.getContractNo());
        
        Result<List<TransferContractStatusVo>> existingContractsResult = transferContractStatusService.getTransferContractStatus(contractNoQuery);
        if (ResultUtil.isModelNotNull(existingContractsResult) && CollectionUtils.isNotEmpty(existingContractsResult.getModel())) {
            return ResultUtil.failResult("合同编号【" + submitContractReq.getContractNo() + "】已存在，不允许重复录入");
        }
        
        String carNumber = submitContractReq.getCarNumber();
        
        // 1. 根据车牌号直接查询车辆信息
        Result<com.ql.rent.vo.vehicle.VehicleInfoVO> vehicleResult = vehicleInfoService.vehicleInfoByLicense(merchantId, carNumber);
        if (ResultUtil.isModelNull(vehicleResult)) {
            String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                    merchantId, carNumber, submitContractReq.getContractNo(), null, "车牌对应的车辆不存在", submitContractReq.getAccount());
            sendMsg(content, merchantId, carNumber);
            return ResultUtil.failResult("车牌号【" + carNumber + "】对应的车辆不存在");
        }

        com.ql.rent.vo.vehicle.VehicleInfoVO vehicleInfo = vehicleResult.getModel();
        Long vehicleId = vehicleInfo.getId();

        // 构建转移合同状态记录
        TransferContractStatusParam statusParam = TransferContractStatusParam.builder()
                .orderId(0L)
                .vehicleId(vehicleId)
                .contractNo(submitContractReq.getContractNo())
                .carNumber(submitContractReq.getCarNumber())
                .carType(submitContractReq.getCarType())
                .driverName(submitContractReq.getDriverName())
                .driverPhone(submitContractReq.getDriverPhone())
                .driverId(submitContractReq.getDriverId())
                .account(submitContractReq.getAccount())
                .beginTime(submitContractReq.getBeginTime())
                .endTime(submitContractReq.getEndTime())
                .merchantId(merchantId)
                .bizType(TransferContractBizTypeEnum.OPEN_API_CALL.getCode()) // Open API调用
                .build();

        // 2. 根据车辆ID查询违章转移绑定记录
        TransferVehicleBindQuery query = new TransferVehicleBindQuery();
        query.setMerchantId(merchantId);
        query.setVehicleId(vehicleId);

        Result<List<VehicleIllegalTransferDetailVo>> bindResult = this.listTransferVehicleBind(query);
        if (ResultUtil.isModelNull(bindResult) || CollectionUtils.isEmpty(bindResult.getModel())) {
            // 发送企微消息
            String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                    merchantId, carNumber, submitContractReq.getContractNo(), null, "车牌未绑定违章转移订单", submitContractReq.getAccount());
            sendMsg(content, merchantId, carNumber);

            // 调用失败，保存失败状态
            statusParam.setStatus(TransferContractStatusEnum.REPORT_FAIL.getValue());
            statusParam.setErrorMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_002.getMessage());
            transferContractStatusService.saveTransferContractStatus(statusParam);

            // 构建返回信息
            SubmitContractResponse submitContractResponse = new SubmitContractResponse();
            submitContractResponse.setContractNo(submitContractReq.getContractNo());
            submitContractResponse.setPostCode(ResultCodeEnum.ViolationCodeEnum.open_violation_001.getCode());
            submitContractResponse.setPostMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_001.getMessage());
            submitContractResponse.setState((int) TransferContractStatusEnum.REPORT_FAIL.getValue());
            return ResultUtil.successResult(submitContractResponse);
        }

        // 3. 检查订单是否可用（已全额支付且未过期）
        List<Long> orderIds = bindResult.getModel().stream()
                .map(VehicleIllegalTransferDetailVo::getOrderId)
                .distinct()
                .collect(Collectors.toList());

        // 直接使用 mallServiceOrderInfoService 检查订单可用性
        MallServiceOrderParam param = new MallServiceOrderParam();
        param.setOrderIds(orderIds);
        param.setOrderStatus(1); // 已全额支付
        param.setStartExpirationDate(new Date()); // 未过有效期

        List<MallServiceOrderInfoDTO> validOrders = mallServiceOrderInfoService.serviceOrderList(merchantId, param);

        if (CollectionUtils.isEmpty(validOrders)) {
            // 发送企微消息
            String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                    merchantId, carNumber, submitContractReq.getContractNo(), null, "车牌未绑定可用的违章转移订单", submitContractReq.getAccount());
            sendMsg(content, merchantId, carNumber);

            // 调用失败，保存失败状态
            statusParam.setStatus(TransferContractStatusEnum.REPORT_FAIL.getValue());
            statusParam.setErrorMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_002.getMessage());
            transferContractStatusService.saveTransferContractStatus(statusParam);

            // 构建返回信息
            SubmitContractResponse submitContractResponse = new SubmitContractResponse();
            submitContractResponse.setContractNo(submitContractReq.getContractNo());
            submitContractResponse.setPostCode(ResultCodeEnum.ViolationCodeEnum.open_violation_001.getCode());
            submitContractResponse.setPostMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_001.getMessage());
            submitContractResponse.setState((int) TransferContractStatusEnum.REPORT_FAIL.getValue());
            return ResultUtil.successResult(submitContractResponse);
        }

        // 4. 检查并补充122账号（如果未提供则查询系统绑定的账号）
        if (submitContractReq.getAccount() == null) {
            TrafficManagementBindDTO bindAccount =
                    trafficManagementBindService.selectIllegalTransferAccount(merchantId, vehicleId);
            if (bindAccount == null) {
                String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                        merchantId, carNumber, submitContractReq.getContractNo(), null, "车牌未绑定122账号", submitContractReq.getAccount());
                sendMsg(content, merchantId, carNumber);

                // 调用失败，保存失败状态
                statusParam.setStatus(TransferContractStatusEnum.REPORT_FAIL.getValue());
                statusParam.setErrorMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_002.getMessage());
                transferContractStatusService.saveTransferContractStatus(statusParam);

                // 构建返回信息
                SubmitContractResponse submitContractResponse = new SubmitContractResponse();
                submitContractResponse.setContractNo(submitContractReq.getContractNo());
                submitContractResponse.setPostCode(ResultCodeEnum.ViolationCodeEnum.open_violation_002.getCode());
                submitContractResponse.setPostMsg(ResultCodeEnum.ViolationCodeEnum.open_violation_002.getMessage());
                submitContractResponse.setState((int) TransferContractStatusEnum.REPORT_FAIL.getValue());
                return ResultUtil.successResult(submitContractResponse);
            }
            submitContractReq.setAccount(bindAccount.getUsername());
            statusParam.setAccount(bindAccount.getUsername());
        }

        // 5. 调用三方接口录入合同
        Result<SubmitContractResponse> responseResult = remoteViolationService.submitContract(submitContractReq);

        // 6. 保存转移合同状态记录
        if (ResultUtil.isModelNull(responseResult)) {
            // 调用失败，保存失败状态
            statusParam.setStatus(TransferContractStatusEnum.REPORT_FAIL.getValue());
            statusParam.setMessage(responseResult.getMessage());
            transferContractStatusService.saveTransferContractStatus(statusParam);

            // 发送企业微信通知
            String content = IllegalTransferMessageTemplate.formatDirectMerchantReportFail(
                    merchantId, carNumber, submitContractReq.getContractNo(), null, responseResult.getMessage(), submitContractReq.getAccount());
            sendMsg(content, merchantId, carNumber);

            SubmitContractResponse submitContractResponse = new SubmitContractResponse();
            submitContractResponse.setContractNo(submitContractReq.getContractNo());
            submitContractResponse.setPostCode(responseResult.getResultCode());
            submitContractResponse.setPostMsg(responseResult.getMessage());
            submitContractResponse.setState((int) TransferContractStatusEnum.REPORT_FAIL.getValue());
            return ResultUtil.successResult(submitContractResponse);
        }

        // 调用成功，保存成功状态和响应信息
        SubmitContractResponse response = responseResult.getModel();
        statusParam.setStatus(response.getState().byteValue());
        statusParam.setContractId(response.getOrderId());
        statusParam.setMessage(response.getPostMsg());
        statusParam.setPostCode(response.getPostCode());
        transferContractStatusService.saveTransferContractStatus(statusParam);

        // 上报中 如果postCode失败，发送企业微信通知
        if (SubmitContractResponse.isPostFail(response) &&
                Integer.valueOf(TransferContractStatusEnum.REPORTING.getValue()).equals(response.getState())) {
            String content = IllegalTransferMessageTemplate.formatDirectMerchantReporting(
                    merchantId, carNumber, submitContractReq.getContractNo(), null, response.getPostMsg(), submitContractReq.getAccount());
            sendMsg(content, merchantId, carNumber);
        }
        return responseResult;
    }

    /**
     * 异步发送企业微信群消息
     * 
     * @param content 消息内容
     */
    private void sendMsg(String content, Long merchantId, String licensePlate) {
        if (content == null || content.trim().isEmpty()) {
            return;
        }

        asyncPromiseExecutor.execute(() ->
                enterpriseWechatService.sendGroupMsgV2(QyWechatConstant.VEHICLE_TRANSFER_ROBOT_KEY, content, merchantId, null));
    }

    @Override
    public Result<Boolean> cancelContract(CancelContractReq cancelContractReq) {
        return remoteViolationService.cancelContract(cancelContractReq);
    }

    @Override
    public Result<ContractDetailResponse> contractDetail(ContractDetailReq contractDetailReq) {
        logger.startLog().with("contractDetailReq", contractDetailReq).log("查询合同详情");
        return remoteViolationService.contractDetail(contractDetailReq);
    }

    @Override
    public Result<Boolean> abortContract(AbortContractReq abortContractReq) {
        logger.startLog().with("abortContractReq", abortContractReq).logAudit("合同终止");
        return remoteViolationService.abortContract(abortContractReq);
    }

    @Override
    public Result<List<ContractListResponse>> contractList(ContractListReq contractListReq) {
        logger.startLog().with("contractListReq", contractListReq).log("查询合同列表");
        return remoteViolationService.contractList(contractListReq);
    }

    @Override
    public Result<Boolean> transferContract(String contractId, String contractFile) {
        logger.startLog().with("contractId", contractId).logAudit("强制转移");

        if (contractId == null || contractFile == null) {
            return ResultUtil.failResult("参数错误");
        }

        // 构造请求
        TransferContractReq transferContractReq = new TransferContractReq();
        transferContractReq.setOrderId(contractId);
        transferContractReq.setContractFile(contractFile);
        
        // 调用远程服务
        return remoteViolationService.transferContract(transferContractReq);
    }

    @Override
    public Result<Long> deductViolationQueryCount(Long merchantId, String carNumber) {
        logger.startLog().with("merchantId", merchantId).with("carNumber", carNumber).log("开始扣减违章查询次数");

        if (merchantId == null || carNumber == null) {
            return ResultUtil.failResult("参数错误");
        }

        try {
            // 1. 首先根据车牌号查找车辆信息
            Result<com.ql.rent.vo.vehicle.VehicleInfoVO> vehicleInfoResult = vehicleInfoService.vehicleInfoByLicense(merchantId, carNumber);
            if (ResultUtil.isModelNull(vehicleInfoResult)) {
                return ResultUtil.failResult("车辆不存在");
            }

            com.ql.rent.vo.vehicle.VehicleInfoVO vehicleInfo = vehicleInfoResult.getModel();
            Long vehicleId = vehicleInfo.getId();

            // 2. 检查车辆是否绑定违章转移订单
            TransferVehicleBindQuery query = new TransferVehicleBindQuery();
            query.setMerchantId(merchantId);
            query.setVehicleId(vehicleId);

            Result<List<VehicleIllegalTransferDetailVo>> transferDetailListResult = this.listTransferVehicleBind(query);

            // 如果车辆绑定了违章转移订单，则检查是否有效
            if (!ResultUtil.isModelNull(transferDetailListResult) && CollectionUtils.isNotEmpty(transferDetailListResult.getModel())) {
                List<VehicleIllegalTransferDetailVo> transferDetailList = transferDetailListResult.getModel();
                List<Long> orderIds = transferDetailList.stream()
                        .map(VehicleIllegalTransferDetailVo::getOrderId)
                        .distinct()
                        .collect(Collectors.toList());

                // 检查违章转移订单是否有效（已支付且未过期）
                MallServiceOrderParam param = new MallServiceOrderParam();
                param.setOrderIds(orderIds);
                param.setOrderStatus(1); // 已全额支付
                param.setStartExpirationDate(new Date()); // 未过期

                List<MallServiceOrderInfoDTO> validTransferOrders = mallServiceOrderInfoService.serviceOrderList(merchantId, param);

                if (CollectionUtils.isNotEmpty(validTransferOrders)) {
                    // 如果是违章转移订单，则无需扣减，直接返回成功
                    logger.startLog().with("carNumber", carNumber).log("车辆绑定有效的违章转移订单，无需扣减次数");
                    return ResultUtil.successResult(-1L); // 使用-1表示违章转移免费查询
                }
            }

            // 3. 查找可用的违章查询订单，按优先级排序：过期时间近的 → 过期时间远的
            List<MallServiceOrderInfoDTO> availableOrders = mallServiceOrderInfoService.availableMallOrderList(merchantId,
                    MallOrderItemEnum.ILLEGAL_SEARCH.getItemType().byteValue());

            if (CollectionUtils.isEmpty(availableOrders)) {
                return ResultUtil.failResult("无可用的违章查询订单");
            }

            // 按过期时间排序，优先使用过期时间近的订单
            availableOrders.sort(Comparator.comparing(MallServiceOrderInfoDTO::getExpirationDate));

            // 4. 尝试扣减次数，避免并发超扣
            for (MallServiceOrderInfoDTO order : availableOrders) {
                if (MallServiceItemConstant.ILLEGAL.TRANSFER.equals(order.getItemSubPackage())) {
                    continue;
                }
                int affectedRows = mallServiceOrderInfoMapperEx.decreaseRemainingCount(order.getId(), 1);
                if (affectedRows > 0) {
                    logger.startLog()
                            .with("orderId", order.getId())
                            .with("remainingCount", order.getRemainingCount() - 1)
                            .log("成功扣减违章查询次数");
                    return ResultUtil.successResult(order.getId());
                }
            }

            return ResultUtil.failResult("扣减失败，可能由于并发导致");

        } catch (Exception e) {
            log.error("扣减违章查询次数异常", e);
            return ResultUtil.failResult("扣减违章查询次数异常：" + e.getMessage());
        }
    }

    @Override
    public Result<Boolean> compensateViolationQueryCount(Long orderId) {
        logger.startLog().with("orderId", orderId).log("开始补偿违章查询次数");

        if (orderId == null || orderId <= 0) {
            return ResultUtil.failResult("订单ID不能为空");
        }

        try {
            int affectedRows = mallServiceOrderInfoMapperEx.increaseRemainingCount(orderId, 1);
            if (affectedRows > 0) {
                return ResultUtil.successResult(true);
            } else {
                log.warn("补偿违章查询次数失败，可能订单不存在，订单ID: {}", orderId);
                return ResultUtil.failResult("补偿失败，订单可能不存在");
            }
        } catch (Exception e) {
            log.error("补偿违章查询次数异常", e);
            return ResultUtil.failResult("补偿违章查询次数异常：" + e.getMessage());
        }
    }
}