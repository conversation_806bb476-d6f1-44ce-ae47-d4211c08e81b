package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.ql.rent.api.aggregate.dto.OrderDetailExtraDTO;
import com.ql.rent.api.aggregate.dto.OrderDiscountExtraDTO;
import com.ql.rent.api.aggregate.dto.OrderExtraDTO;
import com.ql.rent.api.aggregate.model.dto.DailyPriceDTO;
import com.ql.rent.api.aggregate.model.dto.StandardFeeDTO;
import com.ql.rent.api.aggregate.model.remote.ctrip.dto.ExpenseRecordDTO;
import com.ql.rent.client.IOpenThirdService;
import com.ql.rent.common.IRedisService;
import com.ql.rent.dao.slave.trade.OrderSnapshotSlaveMapper;
import com.ql.rent.dao.slave.trade.VehiclePickReturnSlaveMapper;
import com.ql.rent.dao.trade.*;
import com.ql.rent.dto.trade.*;
import com.ql.rent.entity.trade.*;
import com.ql.rent.enums.YesOrNoEnum;
import com.ql.rent.enums.store.IdRelationEnum;
import com.ql.rent.enums.trade.*;
import com.ql.enums.open.OpenVehicleEnum;
import com.ql.rent.param.trade.PickReturnOperationQuery;
import com.ql.rent.param.trade.SelfPickReturnConfirmVO;
import com.ql.rent.param.trade.VehicleDamageOrderQueryParam;
import com.ql.rent.provider.store.StoreInfoServiceImpl;
import com.ql.rent.service.common.IAreaService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.store.IThirdIdRelationService;
import com.ql.rent.service.trade.*;
import com.ql.rent.service.vehicle.IThirdVehicleIdRelationService;
import com.ql.rent.service.vehicle.IVehicleModelService;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.common.AreaVo;
import com.ql.rent.vo.store.LongLatVo;
import com.ql.rent.vo.store.StoreInfoVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.vehicle.BaseVehicleModelVO;
import com.ql.rent.vo.vehicle.VehicleInfoTagVO;
import com.ql.rent.remote.vehicle.vo.response.SyncContractResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.locationtech.jts.geom.Geometry;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.stream.Collectors;

import static com.ql.rent.constant.RedisConstant.MsgTopic.*;
import static com.ql.rent.enums.trade.OrderTypeEnum.NO_WORRIED_SERVICE;

/**
 * @auther musi
 * @date 2023/10/16 15:31
 */
@Slf4j
@Component
public class OrderMsgProducer {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(OrderMsgProducer.class);

    @Resource
    private OrderInfoMapper orderInfoMapper;

    @Resource
    private OrderDepositMapper orderDepositMapper;
    @Resource
    private OrderRemarkMapper orderRemarkMapper;

    @Resource
    private OrderDetailMapper orderDetailMapper;

    @Resource
    private RerentOrderMapper rerentOrderMapper;

    @Resource
    private OrderDailyPriceMapper orderDailyPriceMapper;

    @Resource
    private PayMapper payMapper;
    @Resource
    private IRedisService redisServer;
    @Resource
    private OrderComponent orderComponent;
    @Resource
    private IVehicleReturnExpenseItemService vehicleReturnExpenseItemService;
    @Resource
    private IVehicleIllegalOrderService vehicleIllegalOrderService;
    @Resource
    private IVehicleDamageOrderService vehicleDamageOrderService;
    @Resource
    private IOpenThirdService openThirdService;
    @Resource
    private IOrderMemberService orderMemberService;
    @Resource
    private IThirdVehicleIdRelationService thirdVehicleIdRelationService;
    @Resource
    private IThirdTradeIdRelationService thirdTradeIdRelationService;
    @Resource
    private IThirdIdRelationService thirdIdRelationService;
    @Resource
    private IAreaService areaService;
    @Resource
    private ISelfPickReturnOperationService selfPickReturnOperationService;
    @Resource
    private VehiclePickReturnSlaveMapper vehiclePickReturnSlaveMapper;
    @Resource
    private Executor orderPromiseExecutor;
    @Resource
    private Executor asyncPromiseExecutor;
    @Resource
    private OrderHourlyMapper orderHourlyMapper;
    @Resource
    private OrderSnapshotSlaveMapper orderSnapshotSlaveMapper;
    @Resource
    private IStoreInfoService storeInfoService;
    @Resource
    private IVehicleModelService vehicleModelService;

    /**
     * 发送订单详细消息
     * @param orderId
     */
    public void sendOrderMsg(Long orderId, OrderMsgTypeEnum orderMsgTypeEnum) {
        orderPromiseExecutor.execute(() -> {
            try {
                logger.startLog().with("订单ID", orderId).with("订单事件", orderMsgTypeEnum.getType()).log("订单消息组装");

                OrderDetailMsgDTO orderDetailMsgDTO = getOrderDetailMsgDTO(orderId);
                Map<String, Object> map = new HashMap<>();
                map.put("type", orderMsgTypeEnum.getType());
                map.put("orderMsg", orderDetailMsgDTO);
                map.put("merchantId", orderDetailMsgDTO.getMerchantId());

                // 推送消息给开放平台->直连商家
                openThirdService.msgPush(ORDER_UPDATE, Long.valueOf(orderDetailMsgDTO.getMerchantId()), JSON.toJSONString(map));
                logger.startLog().ext(orderId).with("msg", map).logAudit("订单消息推送成功");
            } catch (Exception e) {
                log.error("发送订单消息失败", e);
            }
        });
    }


    public void sendOrderDepositMsg(OrderDepositMsgDTO orderDepositMsgDTO) {
        orderPromiseExecutor.execute(() -> {
            try {
                Map<String, Object> map = new HashMap<>();
                map.put("depositMsg", orderDepositMsgDTO);
                map.put("merchantId", orderDepositMsgDTO.getMerchantId());

                // 推送消息给开放平台->直连商家
                openThirdService.msgPush(ORDER_DEPOSIT_FREE, orderDepositMsgDTO.getMerchantId(), JSON.toJSONString(map));
//                log.info("订单后补免押消息推送成功, msg={}", JSON.toJSONString(map));
                logger.startLog().with("msg", map).logAudit("订单后补免押消息推送成功");
            } catch (Exception e) {
                log.error("订单后补免押消息推送失败", e);
                return;
            }
        });
    }


    private void setPenaltyAmount(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        PayExample payExample = new PayExample();
        payExample.createCriteria().andOrderIdEqualTo(orderId)
                .andFeeTypeEqualTo(PayFeeTypeEnum.PENALTY_ORDER_CHARGE.getType());
        List<Pay> payList = payMapper.selectByExample(payExample);
        if (CollectionUtils.isNotEmpty(payList)) {
            for (Pay pay : payList) {
                orderDetailMsgDTO.setPenaltyPayAmount(orderDetailMsgDTO.getPenaltyPayAmount() + pay.getActualPayAmount());
            }
        }
        orderDetailMsgDTO.setPenaltyReceivableAmount(orderDetailMsgDTO.getPenaltyPayAmount());
        PayExample reducePayExample = new PayExample();
        reducePayExample.createCriteria().andOrderIdEqualTo(orderId)
                .andFeeTypeEqualTo(PayFeeTypeEnum.PENALTY_ORDER_REDUCE.getType());
        List<Pay> reducePayList = payMapper.selectByExample(reducePayExample);
        Integer discontAmount = 0;
        if (CollectionUtils.isNotEmpty(reducePayList)) {
            for (Pay pay : reducePayList) {
                discontAmount = discontAmount + pay.getActualPayAmount();
            }
            orderDetailMsgDTO.setPenaltyReceivableAmount(orderDetailMsgDTO.getPenaltyReceivableAmount() + discontAmount);
        }
    }


    private void setSelfPrInfo(Long orderId, OrderInfo orderInfo, OrderDetailMsgDTO orderDetailMsgDTO) {
        PickReturnOperationQuery query = new PickReturnOperationQuery();
        query.setOrderId(orderId);
        LoginVo loginVo = new LoginVo();
        loginVo.setMerchantId(orderInfo.getMerchantId());
        Result<List<SelfPickReturnConfirmVO>> prOperationResult =
                selfPickReturnOperationService.getPickReturnOperation(query, loginVo);
        PayExample payExample = new PayExample();
        payExample.createCriteria().andOrderIdEqualTo(orderId)
                .andFeeTypeEqualTo(PayFeeTypeEnum.SELF_PICK_RETURN_CHARGE.getType())
                .andPayStatusEqualTo(PayStatusEnum.ALL_PAID.getStatus());
        List<Pay> pays = payMapper.selectByExample(payExample);

        SelfPrDTO selfPrDTO = new SelfPrDTO();

        List<SelfPrOperationDTO> operationDTOList = new ArrayList<>();
        if (ResultUtil.isModelNotNull(prOperationResult)) {
            for (SelfPickReturnConfirmVO vo : prOperationResult.getModel()) {
                SelfPrOperationDTO dto = new SelfPrOperationDTO();
                dto.setOpType(vo.getOpType().intValue());
                dto.setCarPicList(vo.getAtt().stream().map(att -> {
                    CarImageDTO carImageDTO = new CarImageDTO();
                    carImageDTO.setUrl(att.getUrl());
                    carImageDTO.setType(att.getType());
                    return carImageDTO;
                }).collect(Collectors.toList()));
                dto.setCarProblem(vo.getCarProblem());
                dto.setCarRemark(vo.getCarRemark());
                dto.setCarPlaceNotes(vo.getPositionRemark());
                dto.setStartTime(vo.getStartTime());
                dto.setEndTime(vo.getEndTime());
                dto.setEnergyType(vo.getEnergyType().intValue());
                if (vo.getEnergyType() == 1 || vo.getEnergyType() == 3) { // 油车或油电混动车
                    dto.setMaxOilLiter(vo.getMaxOilLiter());
                    dto.setRemainOil(vo.getOilLiter() != null ? vo.getOilLiter().doubleValue() : null);
                    dto.setOilPercent(vo.getOilPercent() != null ? vo.getOilPercent().doubleValue() : null);
                    dto.setOilUnitPrice(vo.getOilUnitPrice());
                }

                if (vo.getEnergyType() == 2 || vo.getEnergyType() == 3) { // 电车或油电混动车
                    dto.setRange(vo.getRemindMileage() != null ? vo.getRemindMileage().doubleValue() : null);
                    dto.setMaxMileage(vo.getMaxMileage());
                    dto.setElectricPercent(vo.getBatteryPercent() != null ? vo.getBatteryPercent().doubleValue() : null);
                    dto.setMileageUnitPrice(vo.getMileageUnitPrice());
                }

                dto.setServicePrice(vo.getServicePrice());
                dto.setDeviceNo(vo.getDeviceNo());
                dto.setVehicleId(String.valueOf(vo.getVehicleId()));
                dto.setThirdVehicleId(thirdVehicleIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(), OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), vo.getVehicleId(), orderInfo.getMerchantId()));
                operationDTOList.add(dto);
            }
        }
        selfPrDTO.setSelfPrOperationDTOList(operationDTOList);

        selfPrDTO.setRecordList(pays.stream().map(pay -> {
            ExpenseRecordDTO dto = new ExpenseRecordDTO();
            dto.setAmount(Long.valueOf(pay.getActualPayAmount()));
            dto.setSerialNumber(pay.getPaySourceId());
            return dto;
        }).collect(Collectors.toList()));

        orderDetailMsgDTO.setSelfPrDTO(selfPrDTO);
    }

    public static void main(String[] args) {
        String msg = "{\"orderMsg\":{\"depositPayType\":0,\"driverlicNo\":\"\",\"freeDepositDegree\":0,\"idcardNo\":\"310113198402235547\",\"illegalDepositAmount\":0,\"illegalOrderList\":[],\"merchantId\":\"58\",\"mobile\":\"18601740878\",\"orderDiscountList\":[],\"orderId\":\"116155\",\"orderSource\":2,\"orderStatus\":8,\"orderTime\":1699971089302,\"pickupAddr\":\"安亭西站\",\"pickupAddrType\":2,\"pickupCityCode\":\"107\",\"pickupDate\":\"2023-11-15 10:00:00\",\"pickupLongLat\":{\"latitude\":31.311618,\"longitude\":121.156272},\"pickupStoreId\":\"637\",\"priceDailyList\":[{\"date\":\"2023-11-15\",\"hour\":24,\"partDailyPrice\":23800,\"per\":1.0,\"price\":23800}],\"rentDepositAmount\":0,\"returnAddr\":\"安亭西站\",\"returnAddrType\":3,\"returnCityCode\":\"107\",\"returnCostItemList\":[],\"returnDate\":\"2023-11-16 10:00:00\",\"returnLongLat\":{\"latitude\":31.311618,\"longitude\":121.156272},\"returnStoreId\":\"637\",\"serviceItemList\":[],\"sourceOrderId\":\"28846542279\",\"userName\":\"徐春燕\"},\"merchantId\":\"58\",\"type\":\"cancel\"}";
        Map<String, Object> map = JSON.parseObject(msg, Map.class);
        Long merchantId = (Long) map.get("merchantId");
        String pushStr = String.valueOf(map.get("msg"));
        System.out.println(merchantId);
        System.out.println(pushStr);

    }


    private void setUserCertificate(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        Result<UserCertificateVo> userCertificateVoResult = orderMemberService.getUserCertificate(orderId);
        if (userCertificateVoResult.isSuccess() && userCertificateVoResult.getModel() != null) {
            orderDetailMsgDTO.setIdcardNo(userCertificateVoResult.getModel().getIdcardNo());
            orderDetailMsgDTO.setDriverlicNo(userCertificateVoResult.getModel().getDriverlicNo());
            orderDetailMsgDTO.setPassport(userCertificateVoResult.getModel().getPassport());
            orderDetailMsgDTO.setOtherCertificateId(userCertificateVoResult.getModel().getOther());
        }
    }

    @NotNull
    private OrderDetailMsgDTO buildOrderBase(OrderInfo orderInfo) {
        // 门店mapping
        Map<Long, String> storeMapping = thirdIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(),
                IdRelationEnum.STORE.getType(), Arrays.asList(orderInfo.getPickupStoreId(), orderInfo.getReturnStoreId()), orderInfo.getMerchantId());
        // 地区code
        List<Long> cityIds = Arrays.asList(orderInfo.getPickupCityId(), orderInfo.getReturnCityId()).stream().distinct().collect(Collectors.toList());
        Map<Long, AreaVo> areaVoMap = areaService.findByIds(cityIds).getModel().stream()
                .collect(Collectors.toMap(AreaVo::getId, areaVo -> areaVo, (existing, replacement) -> existing));

        OrderDetailMsgDTO orderDetailMsgDTO = new OrderDetailMsgDTO();
        orderDetailMsgDTO.setOpTime(orderInfo.getOpTime());
        orderDetailMsgDTO.setVehicleModelId(String.valueOf(orderInfo.getVehicleModelId()));
        String thirdVehicleModelId = thirdVehicleIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(), OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(), orderInfo.getVehicleModelId(), orderInfo.getMerchantId());
        orderDetailMsgDTO.setThirdVehicleModelId(thirdVehicleModelId);
        orderDetailMsgDTO.setVehicleId(String.valueOf(orderInfo.getVehicleId()));
        orderDetailMsgDTO.setThirdVehicleId(thirdVehicleIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(), OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), orderInfo.getVehicleId(), orderInfo.getMerchantId()));
        orderDetailMsgDTO.setVehicleName(orderInfo.getVehicleName());
        orderDetailMsgDTO.setVehicleNo(orderInfo.getVehicleNo());
        orderDetailMsgDTO.setOrderId(String.valueOf(orderInfo.getId()));
        orderDetailMsgDTO.setOrderSource(Integer.valueOf(orderInfo.getOrderSource()));
        orderDetailMsgDTO.setOrderStatus(Integer.valueOf(orderInfo.getOrderStatus()));
        orderDetailMsgDTO.setSourceOrderId(orderInfo.getSourceOrderId());
        orderDetailMsgDTO.setOrderTime(orderInfo.getOrderTime());
        orderDetailMsgDTO.setMerchantId(String.valueOf(orderInfo.getMerchantId()));
        orderDetailMsgDTO.setUserName(orderInfo.getUserName());
        orderDetailMsgDTO.setMobile(orderInfo.getMobile());
        orderDetailMsgDTO.setPickupAddr(orderInfo.getPickupAddr());
        orderDetailMsgDTO.setReturnAddr(orderInfo.getReturnAddr());
        orderDetailMsgDTO.setPickupAddrType(Integer.valueOf(orderInfo.getPickupAddrType()));
        orderDetailMsgDTO.setReturnAddrType(Integer.valueOf(orderInfo.getReturnAddrType()));
        orderDetailMsgDTO.setPickupCityCode(String.valueOf(orderInfo.getPickupCityId()));
        orderDetailMsgDTO.setPickupCityGaodeCode(areaVoMap.get(orderInfo.getPickupCityId()).getCode());
        orderDetailMsgDTO.setReturnCityCode(String.valueOf(orderInfo.getReturnCityId()));
        orderDetailMsgDTO.setReturnCityGaodeCode(areaVoMap.get(orderInfo.getReturnCityId()).getCode());
        orderDetailMsgDTO.setPickupDate(DateUtil.getFormatDateStr(orderInfo.getPickupDate(), DateUtil.yyyyMMddHHmmss));
        orderDetailMsgDTO.setReturnDate(DateUtil.getFormatDateStr(orderInfo.getReturnDate(), DateUtil.yyyyMMddHHmmss));
        orderDetailMsgDTO.setLastReturnDate(DateUtil.getFormatDateStr(orderInfo.getLastReturnDate(), DateUtil.yyyyMMddHHmmss));
        orderDetailMsgDTO.setPickupStoreId(String.valueOf(orderInfo.getPickupStoreId()));
        orderDetailMsgDTO.setThirdPickupStoreId(storeMapping.get(orderInfo.getPickupStoreId()));
        orderDetailMsgDTO.setReturnStoreId(String.valueOf(orderInfo.getReturnStoreId()));
        orderDetailMsgDTO.setThirdReturnStoreId(storeMapping.get(orderInfo.getReturnStoreId()));
        orderDetailMsgDTO.setMainOrderPayAmount(orderInfo.getPayAmount());
        orderDetailMsgDTO.setMainOrderReceivableAmount(orderInfo.getReceivableAmount());
        orderDetailMsgDTO.setPayAmount(orderInfo.getPayAmount());
        orderDetailMsgDTO.setReceivableAmount(orderInfo.getReceivableAmount());
        orderDetailMsgDTO.setSelfPrOrder(orderInfo.getSelfPrOrder().intValue());
        orderDetailMsgDTO.setSelfPrStatus(orderInfo.getSelfPrStatus().intValue());
        orderDetailMsgDTO.setPickupCityName(areaVoMap.get(orderInfo.getPickupCityId()).getName());
        orderDetailMsgDTO.setReturnCityName(areaVoMap.get(orderInfo.getReturnCityId()).getName());
        orderDetailMsgDTO.setSubSeryId(vehicleModelService.getCtripSubSeryIdByModelId(orderInfo.getVehicleModelId()));

        try {
            if (orderInfo.getPickupAddrGis() != null) {
                Geometry pickupGeometry = StoreInfoServiceImpl.getGeometryByBytes(orderInfo.getPickupAddrGis());
                LongLatVo pickupLongLat = new LongLatVo();
                pickupLongLat.setLongitude(pickupGeometry.getCentroid().getX());
                pickupLongLat.setLatitude(pickupGeometry.getCentroid().getY());
                orderDetailMsgDTO.setPickupLongLat(pickupLongLat);
            }
            if (orderInfo.getReturnAddrGis() != null) {
                Geometry returnGeometry = StoreInfoServiceImpl.getGeometryByBytes(orderInfo.getReturnAddrGis());
                LongLatVo returnLongLat = new LongLatVo();
                returnLongLat.setLongitude(returnGeometry.getCentroid().getX());
                returnLongLat.setLatitude(returnGeometry.getCentroid().getY());
                orderDetailMsgDTO.setReturnLongLat(returnLongLat);
            }
            Result<StoreInfoVo> storeInfoVoResult = storeInfoService.storeInfoBaseFind(orderInfo.getPickupStoreId());
            if (ResultUtil.isModelNotNull(storeInfoVoResult)) {
                orderDetailMsgDTO.setPickupStoreName(storeInfoVoResult.getModel().getStoreName());
            }
            storeInfoVoResult = storeInfoService.storeInfoBaseFind(orderInfo.getReturnStoreId());
            if (ResultUtil.isModelNotNull(storeInfoVoResult)) {
                orderDetailMsgDTO.setReturnStoreName(storeInfoVoResult.getModel().getStoreName());
            }
        } catch (Exception e) {
            log.info("转换高德经纬度异常, orderId={}", orderInfo.getId(), e);
        }
        if (StringUtils.isNotEmpty(orderInfo.getExtra())) {
            OrderExtraDTO orderExtra = JSON.parseObject(orderInfo.getExtra(), OrderExtraDTO.class);
            orderDetailMsgDTO.setCancelSource(orderExtra.getCancelSource());
            orderDetailMsgDTO.setPriceKey(orderExtra.getPriceKey());
            orderDetailMsgDTO.setCancelReason(orderExtra.getCancelReason());
            orderDetailMsgDTO.setCancelTime(orderExtra.getCancelTime());
            orderDetailMsgDTO.setNoWorriedOrder(orderExtra.getNoWorried());
            orderDetailMsgDTO.setAnxinOrder(orderExtra.getAnxin());
            orderDetailMsgDTO.setCtripStandardFee(orderExtra.isCtripStandardFee());
            orderDetailMsgDTO.setPackageId(orderExtra.getPackageId());
            orderDetailMsgDTO.setPriceChannel(orderExtra.getPriceChannel());
            orderDetailMsgDTO.setFeizhuCard(orderExtra.getCardOrderExt());
            orderDetailMsgDTO.setRentCardAttrMap(orderExtra.getRentCardAttrMap());
            orderDetailMsgDTO.setCanceling(orderExtra.getCanceling());
            orderDetailMsgDTO.setIdType(orderExtra.getPickUpIdType());
            orderDetailMsgDTO.setIdNo(orderExtra.getPickUpIdNo());
            orderDetailMsgDTO.setAuthNo(orderExtra.getAuthNo());
            orderDetailMsgDTO.setAuthAmount(orderExtra.getAmount());
            if (StringUtils.isNotEmpty(orderDetailMsgDTO.getIdcardNo())) {
                orderDetailMsgDTO.setIdType(IdentityTypeEnum.ID_CARD.getType().intValue());
                orderDetailMsgDTO.setIdNo(orderDetailMsgDTO.getIdcardNo());
            }
            if (StringUtils.isNotEmpty(orderDetailMsgDTO.getPassport())) {
                orderDetailMsgDTO.setIdType(IdentityTypeEnum.PASSPORT.getType().intValue());
                orderDetailMsgDTO.setIdNo(orderDetailMsgDTO.getPassport());
            }
            if (StringUtils.isNotEmpty(orderDetailMsgDTO.getOtherCertificateId())) {
                orderDetailMsgDTO.setIdType(IdentityTypeEnum.OTHER.getType().intValue());
                orderDetailMsgDTO.setIdNo(orderDetailMsgDTO.getOtherCertificateId());
            }

            if (null != orderExtra.getNoWorried() && orderExtra.getNoWorried() == 1) {
                OrderSnapshotExample snapshotExample = new OrderSnapshotExample();
                snapshotExample.createCriteria().andOrderIdEqualTo(orderInfo.getId()).andSourceIdEqualTo(0L).
                        andSnapshotTypeEqualTo(OrderSnapshotTypeEnum.NO_WORRIED_ITEM.getType()).andOrderTypeEqualTo((byte) 0);
                List<OrderSnapshot> orderSnapshots = orderSnapshotSlaveMapper.selectByExample(snapshotExample);
                if (CollectionUtils.isNotEmpty(orderSnapshots)) {
                    List<StandardFeeDTO> standardFeeDTOList = JSON.parseArray(orderSnapshots.get(0).getContent(), StandardFeeDTO.class);
                    if (CollectionUtils.isNotEmpty(standardFeeDTOList)) {
                        orderDetailMsgDTO.setNoWrriedItemList(standardFeeDTOList);
                    }
                }
            }

            PickupManInfoDTO pickupManInfo = new PickupManInfoDTO();
            pickupManInfo.setPickupName(orderInfo.getUserName());
            pickupManInfo.setPickupMobile(orderInfo.getMobile());
            pickupManInfo.setPickupIdType(orderDetailMsgDTO.getIdType());
            pickupManInfo.setPickupIdNo(orderDetailMsgDTO.getIdNo());
            if (StringUtils.isNotEmpty(orderExtra.getPickUpName())) {
                pickupManInfo.setPickupName(orderExtra.getPickUpName());
                pickupManInfo.setPickupIdNo(orderExtra.getPickUpIdNo());
                pickupManInfo.setPickupIdType(orderExtra.getPickUpIdType());
                pickupManInfo.setPickupMobile(orderExtra.getPickUpMobile());
            }
            orderDetailMsgDTO.setPickupManInfo(pickupManInfo);

            if (orderExtra.getPickupCircleId() != null) {
                if (orderExtra.getPickupCircleId().intValue() ==  -1 || orderExtra.getPickupCircleId().intValue() == 1) {
                    orderDetailMsgDTO.setPickupCircleId(String.valueOf(orderExtra.getPickupCircleId()));
                } else {
                    String thirdCircleId = thirdIdRelationService.getMappingForThird(1L, IdRelationEnum.CICLE.getType(), orderExtra.getPickupCircleId(), orderInfo.getMerchantId());
                    if (StringUtils.isNotBlank(thirdCircleId)) {
                        orderDetailMsgDTO.setPickupCircleId(thirdCircleId);
                    }
                }
            }
            if (orderExtra.getReturnCircleId() != null) {
                if (orderExtra.getReturnCircleId().intValue() ==  -1 || orderExtra.getReturnCircleId().intValue() == 1) {
                    orderDetailMsgDTO.setReturnCircleId(String.valueOf(orderExtra.getReturnCircleId()));
                } else {
                    String thirdCircleId = thirdIdRelationService.getMappingForThird(1L, IdRelationEnum.CICLE.getType(), orderExtra.getReturnCircleId(), orderInfo.getMerchantId());
                    if (StringUtils.isNotBlank(thirdCircleId)) {
                        orderDetailMsgDTO.setReturnCircleId(thirdCircleId);
                    }
                }
            }

            try {
                VehicleInfoTagVO vehicleInfoTagVO = orderComponent.getOriginalVehicleInfo(orderInfo.getId());
//            originalVehicleDTO.setOriginalVehicleId(orderDetailMsgDTO.getVehicleId());
//            originalVehicleDTO.setOriginalVehicleModelId(orderDetailMsgDTO.getVehicleModelId());
//            originalVehicleDTO.setOriginalVehicleName(orderDetailMsgDTO.getVehicleName());
//            originalVehicleDTO.setOriginalVehicleNo(orderDetailMsgDTO.getVehicleNo());
//            originalVehicleDTO.setThirdOriginalVehicleId(orderDetailMsgDTO.getThirdVehicleId());
//            originalVehicleDTO.setThirdOriginalVehicleModelId(orderDetailMsgDTO.getThirdVehicleModelId());

                if (vehicleInfoTagVO != null && !Objects.equals(String.valueOf(vehicleInfoTagVO.getVehicleInfoId()), orderDetailMsgDTO.getVehicleId())) {
                    OriginalVehicleDTO originalVehicleDTO = new OriginalVehicleDTO();
                    originalVehicleDTO.setOriginalVehicleId(String.valueOf(vehicleInfoTagVO.getVehicleInfoId()));
                    originalVehicleDTO.setOriginalVehicleModelId(String.valueOf(vehicleInfoTagVO.getVehicleModel().getId()));
                    originalVehicleDTO.setOriginalVehicleName(vehicleInfoTagVO.getVehicleModel().getVehicleUnionName());
                    originalVehicleDTO.setOriginalVehicleNo(vehicleInfoTagVO.getLicense());
                    String thirdOriginalVehicleModelId = thirdVehicleIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(), OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_MODEL.getType(), vehicleInfoTagVO.getVehicleModel().getId(), orderInfo.getMerchantId());
                    originalVehicleDTO.setThirdOriginalVehicleModelId(thirdOriginalVehicleModelId);
                    String thirdOriginalVehicleId = thirdVehicleIdRelationService.getMappingForThird(OrderSourceEnum.OFFLINE.getSource().longValue(), OpenVehicleEnum.IdRelationTypeEnum.VEHICLE_INFO.getType(), vehicleInfoTagVO.getVehicleInfoId(), orderInfo.getMerchantId());
                    originalVehicleDTO.setThirdOriginalVehicleId(thirdOriginalVehicleId);
                    orderDetailMsgDTO.setOriginalVehicleInfo(originalVehicleDTO);
                    log.info("订单消息推送, 订单有过改排, orderId={}, 原始车辆信息={}", orderInfo.getId(), JSON.toJSONString(originalVehicleDTO));
                }
            } catch (Exception e) {
                log.info("订单消息推送, 获取订单原始车型异常, orderId={}", orderInfo.getId());
            }
        }
        if (orderDetailMsgDTO.isCtripStandardFee()) {
            OrderHourlyExample orderHourlyExample = new OrderHourlyExample();
            orderHourlyExample.createCriteria().andOrderIdEqualTo(orderInfo.getId()).andMerchantIdEqualTo(orderInfo.getMerchantId());
            List<OrderHourly> orderHourlies = orderHourlyMapper.selectByExample(orderHourlyExample);
            List<OrderHourlyDTO> orderHourlieList = new ArrayList<>();
            orderDetailMsgDTO.setOrderHourlyList(orderHourlieList);
            if (CollectionUtils.isNotEmpty(orderHourlies)) {
                for (OrderHourly orderHourly : orderHourlies) {
                    OrderHourlyDTO orderHourlyDTO = new OrderHourlyDTO();
                    BeanUtils.copyProperties(orderHourly, orderHourlyDTO);
                    orderHourlieList.add(orderHourlyDTO);
                }
            }
        }
        return orderDetailMsgDTO;
    }

    private void setRemark(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        OrderRemarkExample remarkExample = new OrderRemarkExample();
        remarkExample.createCriteria().andOrderIdEqualTo(orderId).andDeletedEqualTo((byte) 0);
        List<OrderRemark> remarks = orderRemarkMapper.selectByExample(remarkExample);
        if (CollectionUtils.isNotEmpty(remarks)) {
            orderDetailMsgDTO.setRemarks(remarks.stream().map(r -> r.getRemark()).collect(Collectors.toList()));
        }
    }

    private void setDeposit(Long orderId, OrderInfo orderInfo, OrderDetailMsgDTO orderDetailMsgDTO) {
        orderDetailMsgDTO.setFreeDepositDegree(orderInfo.getFreeDepositDegree());
        orderDetailMsgDTO.setDepositStatus(OrderDepositStatusEnum.NOT_FROZEN.getStatus().intValue());
        if (orderDetailMsgDTO.getFreeDepositDegree() == FreeDepositTypeEnum.FREE_ALL.getType().intValue()) {
            orderDetailMsgDTO.setDepositStatus(OrderDepositStatusEnum.FROZEN.getStatus().intValue());
        }
        OrderDepositExample depositExample = new OrderDepositExample();
        depositExample.createCriteria().andOrderIdEqualTo(orderId).andDeletedEqualTo((byte) 0);
        List<OrderDeposit> orderDepositList = orderDepositMapper.selectByExample(depositExample);
        if (CollectionUtils.isNotEmpty(orderDepositList)) {
            OrderDeposit orderDeposit = orderDepositList.get(0);
            orderDetailMsgDTO.setDepositPayType(Integer.valueOf(orderDeposit.getPayType()));
            orderDetailMsgDTO.setIllegalDepositAmount(orderDeposit.getIllegalOrderDepositPayAmount());
            orderDetailMsgDTO.setRentDepositAmount(orderDeposit.getVehicleDepositPayAmount());

            if (OrderSourceEnum.DIDI.getSource().intValue() == orderInfo.getOrderSource().intValue()) {
                OrderDepositExtraVo extraDTO = new OrderDepositExtraVo();
                if (StringUtils.isNotEmpty(orderDeposit.getExtra())) {
                    extraDTO = JSON.parseObject(orderDeposit.getExtra(), OrderDepositExtraVo.class);
                    orderDetailMsgDTO.setFrozenTime(extraDTO.getFrozenTime());
                    if (orderDetailMsgDTO.getFreeDepositDegree() == FreeDepositTypeEnum.FREE_ALL.getType().intValue()) {
                        if (extraDTO.getDepositAmount() == 0) {
                            orderDetailMsgDTO.setDepositStatus(OrderDepositStatusEnum.THAWED.getStatus().intValue());
                            orderDetailMsgDTO.setThawTime(extraDTO.getThawTime());
                        } else {
                            if (CollectionUtils.isNotEmpty(extraDTO.getUnfreezeExtraVos())) {
                               for (OrderDepositUnfreezeExtraVo orderDepositUnfreezeExtraVo : extraDTO.getUnfreezeExtraVos()) {
                                   if (orderDepositUnfreezeExtraVo.getStatus() == 1) {
                                       orderDetailMsgDTO.setDepositStatus(OrderDepositStatusEnum.PART_THAWED.getStatus().intValue());
                                       break;
                                   }
                               }
                            }
                        }
                    }
                }
            }
        }
    }

    private List<Long> setRerentOrder(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        List<Long> rerentOrderIdList = new ArrayList<>();
        List<RerentOrderDTO> rerentList = new ArrayList<>();
        orderDetailMsgDTO.setRerentOrderList(rerentList);
        List<RerentOrder> rerentOrderList = orderComponent.getRerentOrders(Arrays.asList(orderId));
        if (CollectionUtils.isNotEmpty(rerentOrderList)) {
            rerentOrderIdList = rerentOrderList.stream().map(RerentOrder::getId).collect(Collectors.toList());
            for (RerentOrder rerentOrder : rerentOrderList) {
                RerentOrderDTO rerentOrderDTO = buildRerentOrderDTO(rerentList, rerentOrder, orderDetailMsgDTO);
                // 续租服务项
                OrderDetailExample detailExample = new OrderDetailExample();
                detailExample.createCriteria().andOrderIdEqualTo(rerentOrder.getId()).
                        andOrderStatusEqualTo(OrderDetailStatusEnum.CONFIRMED.getStatus()).
                        andParentOrderIdEqualTo(orderId);
                List<OrderDetail> orderDetailList = orderDetailMapper.selectByExample(detailExample);
                List<OrderServiceItemDTO> items = buildOrderServiceItem(orderDetailList, orderDetailMsgDTO, false);
                rerentOrderDTO.setServiceItemList(items);
                orderDetailMsgDTO.setPayAmount(orderDetailMsgDTO.getPayAmount() + rerentOrderDTO.getPayAmount());
                orderDetailMsgDTO.setReceivableAmount(orderDetailMsgDTO.getReceivableAmount() + rerentOrderDTO.getReceivableAmount());
            }
        }

        // 取消的续租单
        RerentOrderExample rerentOrderExample = new RerentOrderExample();
        rerentOrderExample.createCriteria().andOrderIdEqualTo(orderId).andOrderStatusEqualTo(OrderDetailStatusEnum.CANCELLED.getStatus());
        List<RerentOrder> cancelRerentOrderList = rerentOrderMapper.selectByExample(rerentOrderExample);
        if (CollectionUtils.isNotEmpty(cancelRerentOrderList)) {
            for (RerentOrder rerentOrder : cancelRerentOrderList) {
                RerentOrderDTO rerentOrderDTO = buildRerentOrderDTO(rerentList, rerentOrder, orderDetailMsgDTO);
                OrderDetailExample detailExample = new OrderDetailExample();
                detailExample.createCriteria().andOrderIdEqualTo(rerentOrder.getId()).
                        andOrderStatusEqualTo(OrderDetailStatusEnum.CANCELLED.getStatus()).
                        andParentOrderIdEqualTo(orderId);
                List<OrderDetail> orderDetailList = orderDetailMapper.selectByExample(detailExample);
                List<OrderServiceItemDTO> items = buildOrderServiceItem(orderDetailList, orderDetailMsgDTO, false);
                rerentOrderDTO.setServiceItemList(items);

                PayExample payExample = new PayExample();
                payExample.createCriteria().andOrderIdEqualTo(orderId).andSubOrderIdEqualTo(rerentOrder.getId())
                        .andFeeTypeIn(Arrays.asList(PayFeeTypeEnum.PENALTY_RERENT_ORDER_CHARGE.getType()));
                List<Pay> payList = payMapper.selectByExample(payExample);
                if (CollectionUtils.isNotEmpty(payList)) {
                    rerentOrderDTO.setPenaltyPayAmount(payList.get(0).getActualPayAmount());
                } else {
                    rerentOrderDTO.setPenaltyPayAmount(0);
                }
            }
        }
        return rerentOrderIdList;
    }

    @NotNull
    private RerentOrderDTO buildRerentOrderDTO(List<RerentOrderDTO> rerentList, RerentOrder rerentOrder, OrderDetailMsgDTO orderDetailMsgDTO) {
        RerentOrderDTO rerentOrderDTO = new RerentOrderDTO();
        rerentOrderDTO.setRerentOrderId(String.valueOf(rerentOrder.getId()));
        rerentOrderDTO.setSourceRerentOrderId(rerentOrder.getSourceOrderId());
        rerentOrderDTO.setOrderTime(rerentOrder.getOrderTime());
        rerentOrderDTO.setEndDate(DateUtil.getFormatDateStr(rerentOrder.getEndTime(), DateUtil.yyyyMMddHHmmss));
        rerentOrderDTO.setStartDate(DateUtil.getFormatDateStr(rerentOrder.getStartTime(), DateUtil.yyyyMMddHHmmss));
        rerentOrderDTO.setPayAmount(rerentOrder.getPayAmount());
        rerentOrderDTO.setReceivableAmount(rerentOrder.getReceivableAmount());
        rerentOrderDTO.setOrderStatus(rerentOrder.getOrderStatus().intValue());
        // 续租价格日历
        OrderDailyPriceExample orderDailyPriceExample = new OrderDailyPriceExample();
        orderDailyPriceExample.createCriteria().andOrderIdEqualTo(rerentOrder.getId()).andOrderTypeEqualTo((byte) 1);
        List<OrderDailyPrice> orderDailyPricelist = orderDailyPriceMapper.selectByExample(orderDailyPriceExample);
        List<DailyPriceDTO> priceDailyList = buildPriceDailyList(orderDailyPricelist);
        rerentOrderDTO.setPriceDailyList(priceDailyList);
        if (null != orderDetailMsgDTO.getNoWorriedOrder() && orderDetailMsgDTO.getNoWorriedOrder() == 1) {
            OrderSnapshotExample snapshotExample = new OrderSnapshotExample();
            snapshotExample.createCriteria().andOrderIdEqualTo(rerentOrder.getId()).andSourceIdEqualTo(rerentOrder.getOrderId()).
                    andSnapshotTypeEqualTo(OrderSnapshotTypeEnum.NO_WORRIED_ITEM.getType()).andOrderTypeEqualTo((byte) 1);
            List<OrderSnapshot> orderSnapshots = orderSnapshotSlaveMapper.selectByExample(snapshotExample);
            if (CollectionUtils.isNotEmpty(orderSnapshots)) {
                List<StandardFeeDTO> standardFeeDTOList = JSON.parseArray(orderSnapshots.get(0).getContent(), StandardFeeDTO.class);
                if (CollectionUtils.isNotEmpty(standardFeeDTOList)) {
                    rerentOrderDTO.setNoWrriedItemList(standardFeeDTOList);
                }
            }
        }
        rerentList.add(rerentOrderDTO);
        return rerentOrderDTO;
    }


    private List<OrderServiceItemDTO> buildOrderServiceItem(List<OrderDetail> orderDetailList, OrderDetailMsgDTO orderDetailMsgDTO, boolean isMainOrder) {
        List<OrderServiceItemDTO> items = new ArrayList<>();

        boolean containNoWorried = false;
        if (CollectionUtils.isNotEmpty(orderDetailList)) {
            for (OrderDetail orderDetail : orderDetailList) {
                OrderServiceItemDTO item = new OrderServiceItemDTO();
                item.setName(orderDetail.getName());
                item.setPrice(orderDetail.getPrice());
                item.setType(Integer.valueOf(orderDetail.getOrderType()));
                item.setAmount(orderDetail.getAmount());
                item.setQuantity(orderDetail.getQuantity());
                item.setPayMode(Integer.valueOf(orderDetail.getPayMode()));
                if (StringUtils.isNotBlank(orderDetail.getExtra())) {
                    OrderDetailExtraDTO extraDTO = JSON.parseObject(orderDetail.getExtra(), OrderDetailExtraDTO.class);
                    item.setCode(extraDTO.getCode());
                    item.setRemark(extraDTO.getRemark());
                    item.setAddOrderId(extraDTO.getAddOrderId());
                    item.setCtripStandardFee(extraDTO.isCtripStandardFee());
                    if (extraDTO.getCalPer() != null) {
                        item.setCalPer(extraDTO.getCalPer());
                    }
                }
                if (Objects.equals(item.getCode(), ServiceFeeTypeEnum.NO_WORRIED_SERVICE.getServiceCode())) {
                    containNoWorried = true;
                    try {
                        if (item.getCalPer() != 0) {
                            // 使用 Math.round() 方法实现四舍五入，并将结果转换为 int 类型
                            item.setPrice((int) Math.round((double) item.getAmount() / item.getCalPer()));
                        }
                    } catch (Exception e) {
                        log.error("buildOrderServiceItem error, item={}", JSON.toJSONString(item), e);
                    }
                }

                if (Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_PICKUIP_SERVICE.getServiceCode())
                        || Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_PICKUIP_ARRIVE_SERVICE.getServiceCode())
                        || Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_PICKUIP_TRANCS_SERVICE.getServiceCode())) {
                    item.setType(Integer.valueOf(OrderTypeEnum.SEND_SERVICE.getType()));
                }
                if (Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_RETURN_SERVICE.getServiceCode())
                        || Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_RETURN_ARRIVE_SERVICE.getServiceCode())
                        || Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_RETURN_TRANCS_SERVICE.getServiceCode())
                        || Objects.equals(item.getCode(), ServiceFeeTypeEnum.STORE_DIFFPLACE_SERVICE.getServiceCode())) {
                    item.setType(Integer.valueOf(OrderTypeEnum.PICK_SERVICE.getType()));
                }
                items.add(item);
            }
        }

        try {
            if (!containNoWorried && isMainOrder && orderDetailMsgDTO.getNoWorriedOrder() != null && orderDetailMsgDTO.getNoWorriedOrder() == 1 && CollectionUtils.isNotEmpty(items)) {
                List<OrderServiceItemDTO> noWorriedItems = new ArrayList<>();

                OrderServiceItemDTO noWorriedItem = new OrderServiceItemDTO();
                noWorriedItem.setName(ServiceFeeTypeEnum.NO_WORRIED_SERVICE.getServiceName());
                noWorriedItem.setCode(ServiceFeeTypeEnum.NO_WORRIED_SERVICE.getServiceCode());
                noWorriedItem.setType(NO_WORRIED_SERVICE.getType().intValue());
                noWorriedItem.setAmount(0);

                for (OrderServiceItemDTO item : items) {
                    if (item.getPayMode() == 1) {
                        noWorriedItems.add(item);
                        continue;
                    }
                    noWorriedItem.setAmount(noWorriedItem.getAmount() + item.getAmount());
                    if (Objects.equals(item.getCode(), ServiceFeeTypeEnum.RENT_FEE_SERVICE.getServiceCode())) {
                        noWorriedItem.setQuantity(item.getQuantity());
                        noWorriedItem.setPayMode(item.getPayMode());
                        noWorriedItem.setCtripStandardFee(item.isCtripStandardFee());
                        noWorriedItem.setCalPer(item.getCalPer());
                    }
                }
                // 保留整数, noWorriedItem.getAmount()/noWorriedItem.getCalPer()
                noWorriedItem.setPrice((int) (noWorriedItem.getAmount() / noWorriedItem.getCalPer()));
                noWorriedItems.add(noWorriedItem);
                return noWorriedItems;
            }
        } catch (Exception e) {
            log.info("合并一口价无忧租异常, orderId={}", orderDetailMsgDTO.getOrderId(), e);
        }

        return items;
    }

    private List<OrderServiceItemDTO> setServiceItem(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        OrderDetailExample detailExample = new OrderDetailExample();
        detailExample.createCriteria().andOrderIdEqualTo(orderId).
                andOrderStatusEqualTo(OrderDetailStatusEnum.CONFIRMED.getStatus()).
                andParentOrderIdEqualTo(0L);
        List<OrderDetail> orderDetailList = orderDetailMapper.selectByExample(detailExample);
        List<OrderServiceItemDTO> items = buildOrderServiceItem(orderDetailList, orderDetailMsgDTO, true);
        orderDetailMsgDTO.setServiceItemList(items);
        return items;
    }

    private void setDiscount(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO, List<Long> rerentOrderIdList, List<OrderServiceItemDTO> items) {
        Map<Long, List<OrderDiscount>> discountMap = orderComponent.getDiscountMap(Arrays.asList(orderId), rerentOrderIdList);
        List<OrderDiscountDTO> discountList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(discountMap.get(orderId))) {
            for (OrderDiscount discount : discountMap.get(orderId)) {
                if (discount.getDiscountKind().intValue() == OrderDiscountKindEnum.SUPPLEMENTARY_AMOUNT.getType().intValue()) {
                    OrderServiceItemDTO item = new OrderServiceItemDTO();
                    item.setName("补充金额");
                    item.setPrice(discount.getDiscountAmount());
                    item.setType(100);
                    item.setAmount(discount.getDiscountAmount());
                    item.setQuantity(1);
                    item.setPayMode(1);
                    items.add(item);
                    orderDetailMsgDTO.setPayAmount(orderDetailMsgDTO.getPayAmount() + item.getAmount());
                    orderDetailMsgDTO.setReceivableAmount(orderDetailMsgDTO.getReceivableAmount() + item.getAmount());
                    continue;
                }
                OrderDiscountDTO item = new OrderDiscountDTO();
                BeanUtils.copyProperties(discount, item);
                if (StringUtils.isNotBlank(discount.getExtra())) {
                    OrderDiscountExtraDTO extraDTO = JSON.parseObject(discount.getExtra(), OrderDiscountExtraDTO.class);
                    if (extraDTO != null && StringUtils.isNotBlank(extraDTO.getName())) {
                        item.setDiscountName(extraDTO.getName());
                    }
                }
                discountList.add(item);
                if (orderId.intValue() == discount.getOrderId().intValue()
                        && discount.getDiscountKind().intValue() != OrderDiscountKindEnum.PENALTY_REDUCE.getType().intValue()
                        && discount.getDiscountKind().intValue() != OrderDiscountKindEnum.ACTIVITY.getType().intValue()
                        && discount.getDiscountKind().intValue() != OrderDiscountKindEnum.COUPON.getType().intValue()) {
                    orderDetailMsgDTO.setPayAmount(orderDetailMsgDTO.getPayAmount() - discount.getMerchantAssumeAmount());
                }

            }
        }
        orderDetailMsgDTO.setOrderDiscountList(discountList);
    }

    private void setReturnCostItem(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        Result<List<VehicleReturnExpenseItemVO>> returnCostItemListResult = vehicleReturnExpenseItemService.getByOrderId(orderId, null, false);
        List<ReturnCostItemDTO> returnCostItemList = new ArrayList<>();
        if (returnCostItemListResult.isSuccess() && CollectionUtils.isNotEmpty(returnCostItemListResult.getModel())) {
            for (VehicleReturnExpenseItemVO itemVO : returnCostItemListResult.getModel()) {
                ReturnCostItemDTO item = new ReturnCostItemDTO();
                item.setItemType(Integer.valueOf(itemVO.getItemType()));
                item.setName(itemVO.getItemName());
                item.setAmount(Math.toIntExact(itemVO.getExpenseAmount()));
                item.setAttUrlList(itemVO.getAttUrlList());
                item.setCreateTime(itemVO.getCreateTime());
                item.setExpenseItemType(itemVO.getExpenseItemPropId().intValue());
                if (itemVO.getRefundPayType().intValue() == 1) {
                    item.setPayMode(2);
                } else if (itemVO.getRefundPayType().intValue() == 2) {
                    item.setPayMode(1);
                }
                returnCostItemList.add(item);
                if (itemVO.getItemType() == 1) {
                    orderDetailMsgDTO.setPayAmount(orderDetailMsgDTO.getPayAmount() + itemVO.getExpenseAmount().intValue());
                    orderDetailMsgDTO.setReceivableAmount(orderDetailMsgDTO.getReceivableAmount() + itemVO.getExpenseAmount().intValue());
                } else {
                    orderDetailMsgDTO.setPayAmount(orderDetailMsgDTO.getPayAmount() - itemVO.getExpenseAmount().intValue());
                    orderDetailMsgDTO.setReceivableAmount(orderDetailMsgDTO.getReceivableAmount() - itemVO.getExpenseAmount().intValue());
                }
            }
        }
        orderDetailMsgDTO.setReturnCostItemList(returnCostItemList);
    }

    private void setDamage(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        VehicleDamageOrderQueryParam queryParam = new VehicleDamageOrderQueryParam();
        queryParam.setOrderId(orderId);
        Result<List<VehicleDamageOrderVO>> damageOrderResult = vehicleDamageOrderService.listVehicleDamageOrder(queryParam);
        if (damageOrderResult.isSuccess() && CollectionUtils.isNotEmpty(damageOrderResult.getModel())) {
            List<DamageOrderDTO> damageOrderList = new ArrayList<>();
            for (VehicleDamageOrderVO damageOrderVO : damageOrderResult.getModel()) {
                DamageOrderDTO damageOrderDTO = new DamageOrderDTO();
                BeanUtils.copyProperties(damageOrderVO, damageOrderDTO);
                if (CollectionUtils.isNotEmpty(damageOrderVO.getDamageProofList())) {
                    damageOrderDTO.setAttUrlList(damageOrderVO.getDamageProofList().stream().map(i -> i.getProofUrl()).collect(Collectors.toList()));
                }
                damageOrderDTO.setDamageOrderId(damageOrderVO.getId());
                damageOrderDTO.setRepairFee(Math.toIntExact(damageOrderVO.getRepairFee()));
                damageOrderDTO.setOutageFee(Math.toIntExact(damageOrderVO.getOutageFee()));
                damageOrderDTO.setDepreciationFee(Math.toIntExact(damageOrderVO.getDepreciationFee()));
                damageOrderDTO.setOtherFee(Math.toIntExact(damageOrderVO.getOtherFee()));
                damageOrderDTO.setSource(Integer.valueOf(damageOrderVO.getSource()));
                damageOrderDTO.setThirdCreateTime(damageOrderVO.getPartnerCreateTime());
                if (damageOrderVO.getDeductionType().intValue() == 1) {
                    damageOrderDTO.setPayMode(2);
                } else if (damageOrderVO.getDeductionType().intValue() == 2) {
                    damageOrderDTO.setPayMode(1);
                }
                damageOrderDTO.setRefundAmount(Math.toIntExact(damageOrderVO.getRefundAmount()));
                damageOrderDTO.setDeductionStatus(Integer.valueOf(damageOrderVO.getDeductionStatus()));
                damageOrderDTO.setDeductionAmount(damageOrderVO.getDeductionAmount());
                damageOrderList.add(damageOrderDTO);
            }
            orderDetailMsgDTO.setDamageOrderList(damageOrderList);
        }
    }

    private void setDailyPrice(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        OrderDailyPriceExample orderDailyPriceExample = new OrderDailyPriceExample();
        orderDailyPriceExample.createCriteria().andOrderIdEqualTo(orderId).andOrderTypeEqualTo((byte)0);
        List<OrderDailyPrice> orderDailyPricelist = orderDailyPriceMapper.selectByExample(orderDailyPriceExample);
        if (CollectionUtils.isEmpty(orderDailyPricelist)) {
            try {
                Thread.sleep(1000);

                orderDailyPricelist = orderDailyPriceMapper.selectByExample(orderDailyPriceExample);
                log.info("订单消息推送, 未获取到价格日历, 线程休眠1秒再查价格日历, orderId={}, orderDailyPricelist={}", orderId, JSON.toJSONString(orderDailyPricelist));
            } catch (InterruptedException e) {
                e.printStackTrace();
            }
        }
        orderDetailMsgDTO.setPriceDailyList(buildPriceDailyList(orderDailyPricelist));
    }

    private void setIllegal(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        Result<List<VehicleIllegalOrderVO>> illegalOrderResult = vehicleIllegalOrderService.listIllegalOrderByOrderIds(Arrays.asList(orderId));
        if (illegalOrderResult.isSuccess() && illegalOrderResult.getModel() != null) {
            List<IllegalOrderDTO> illegalOrderList = new ArrayList<>();
            for (VehicleIllegalOrderVO vo : illegalOrderResult.getModel()) {
                IllegalOrderDTO illegalOrderDTO = new IllegalOrderDTO();
                BeanUtils.copyProperties(vo, illegalOrderDTO);
                illegalOrderDTO.setIllegalOrderId(vo.getId());
                illegalOrderDTO.setIllegalCityId(String.valueOf(vo.getIllegalCityId()));
                illegalOrderDTO.setPenaltyAmount(Math.toIntExact(vo.getPenaltyAmount()));
                illegalOrderDTO.setIllegalType(Integer.valueOf(vo.getIllegalType()));
                illegalOrderDTO.setAgencyFee(Math.toIntExact(vo.getAgencyFee()));
                illegalOrderDTO.setDeductionAmount(Math.toIntExact(vo.getDeductionAmount()));
                illegalOrderDTO.setRefundAmount(Math.toIntExact(vo.getRefundAmount()));
                illegalOrderDTO.setContractDamageAmount(Math.toIntExact(vo.getContractDamageAmount()));

                illegalOrderDTO.setDeductionStatus(Integer.valueOf(vo.getDeductionStatus()));
                if (CollectionUtils.isNotEmpty(vo.getProofList())) {
                    illegalOrderDTO.setAttUrlList(vo.getProofList().stream().map(i -> i.getProofUrl()).collect(Collectors.toList()));
                }

                if (vo.getDeductionType().intValue() == 1) {
                    illegalOrderDTO.setPayMode(2);
                } else if (vo.getDeductionType().intValue() == 2) {
                    illegalOrderDTO.setPayMode(1);
                }
                illegalOrderDTO.setSource(Integer.valueOf(vo.getSource()));
                illegalOrderList.add(illegalOrderDTO);
            }
            orderDetailMsgDTO.setIllegalOrderList(illegalOrderList);
        }
    }


    private List<DailyPriceDTO> buildPriceDailyList(List<OrderDailyPrice> orderDailyPricelist) {
        List<DailyPriceDTO> priceDailyList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(orderDailyPricelist)) {
            for (OrderDailyPrice orderDailyPrice : orderDailyPricelist) {
                DailyPriceDTO dailyPriceDTO = new DailyPriceDTO();
                BeanUtils.copyProperties(orderDailyPrice, dailyPriceDTO);
                dailyPriceDTO.setDate(orderDailyPrice.getRentDate());
                dailyPriceDTO.setPer(orderDailyPrice.getHourPre());
                dailyPriceDTO.setHour(orderDailyPrice.getRentHour());
                priceDailyList.add(dailyPriceDTO);
            }

        }
        return priceDailyList;
    }


    public void sendDeductionRefundMsg(OrderInfo orderInfo, DeductionNotifyDTO notify, Long saasDeductionId) {
        orderPromiseExecutor.execute(() -> {
            if (saasDeductionId != null) {
                String third =
                    thirdTradeIdRelationService.getMappingForThird(1L, notify.getDeductionType().byteValue(),
                        saasDeductionId, orderInfo.getMerchantId());
                notify.setDeductionId(third);
            }
            notify.setSaasDeductionId(saasDeductionId);
            notify.setMerchantId(orderInfo.getMerchantId());
            Map<String, Object> map = new HashMap<>();
            map.put("merchantId", orderInfo.getMerchantId());
            map.put("type", OrderMsgTypeEnum.DEDUCTION.getType());
            map.put("payMsg", notify);
            // 推送消息给开放平台->直连商家
            openThirdService.msgPush(ORDER_DEDUCTION_REFUND, orderInfo.getMerchantId(), JSON.toJSONString(map));
        });
    }

    public void sendOrderTransferMsg(Long merchantId, String originReq, Long channelId, String method) {
        if (merchantId == null) {
            return;
        }
        orderPromiseExecutor.execute(() -> {
            Map<String, Object> map = new HashMap<>();
            map.put("merchantId", merchantId);
            map.put("channelId", channelId);
            map.put("transferMsg", originReq);
            // 推送消息给开放平台->直连商家
            openThirdService.msgPush(method, merchantId, JSON.toJSONString(map));
        });

    }


    public void pushContract(Long merchantId, OrderContractDTO orderContractDTO) {
        asyncPromiseExecutor.execute(() -> {
            try {
                Map<String, Object> map = new HashMap<>();
                map.put("contractMsg", orderContractDTO);
                map.put("merchantId", merchantId);
                // 推送消息给开放平台->直连商家
                openThirdService.msgPush(ORDER_CONTRACT, merchantId, JSON.toJSONString(map));
                log.info("推送open合同成功, msg={}", JSON.toJSONString(map));
            } catch (Exception e) {
                log.error("推送open合同失败", e);
            }
        });
    }


    public void pushUserDocument(Long merchantId, Long orderId) {
        asyncPromiseExecutor.execute(() -> {
            try {
                Map<String, Object> map = new HashMap<>();
                OrderUserDocumentDTO orderUserDocumentDTO = this.getOrderUserDocumentDTO(orderId);
                map.put("contractMsg", orderUserDocumentDTO);
                map.put("merchantId", merchantId);
                // 推送消息给开放平台->直连商家
                openThirdService.msgPush(ORDER_USER_DOCUMENT, merchantId, JSON.toJSONString(map));
                log.info("推送open用户信息成功, msg={}", JSON.toJSONString(map));
            } catch (Exception e) {
                log.error("推送open用户信息失败", e);
            }
        });
    }

    private OrderUserDocumentDTO getOrderUserDocumentDTO(Long orderId) {
        OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);
        Result<UserCertificateVo> userCertificateVoResult = orderMemberService.getUserCertificate(orderId);

        OrderUserDocumentDTO result = new OrderUserDocumentDTO();
        result.setOrderId(orderId);
        result.setThirdOrderId(orderInfo.getSourceOrderId());
        result.setChannelId(orderInfo.getOrderSource().intValue());
        if (userCertificateVoResult.isSuccess() && userCertificateVoResult.getModel() != null) {
            UserCertificateVo userCertificateVo = userCertificateVoResult.getModel();
            result.setIdcardNo(userCertificateVo.getIdcardNo());
            result.setIdcardMainImageUrl(userCertificateVo.getIdcardMainImageUrl());
            result.setIdcardSubImageUrl(userCertificateVo.getIdcardSubImageUrl());
            result.setDriverlicNo(userCertificateVo.getDriverlicNo());
            result.setDriverlicMainImageUrl(userCertificateVo.getDriverlicMainImageUrl());
            result.setDriverlicSubImageUrl(userCertificateVo.getDriverlicSubImageUrl());
        }
        return result;
    }

    public OrderDetailMsgDTO getOrderDetailMsgDTO(Long orderId) {

        try {
            OrderInfo orderInfo = orderInfoMapper.selectByPrimaryKey(orderId);

            // 订单基础信息
            OrderDetailMsgDTO orderDetailMsgDTO = buildOrderBase(orderInfo);
            // 备注
            setRemark(orderId, orderDetailMsgDTO);
            // 押金
            setDeposit(orderId, orderInfo, orderDetailMsgDTO);
            // 续租
            List<Long> rerentOrderIdList = setRerentOrder(orderId, orderDetailMsgDTO);
            // 主单费用项
            List<OrderServiceItemDTO> items = setServiceItem(orderId, orderDetailMsgDTO);
            // 优惠活动
            setDiscount(orderId, orderDetailMsgDTO, rerentOrderIdList, items);
            // 退费扣款
            setReturnCostItem(orderId, orderDetailMsgDTO);
            // 车损
            setDamage(orderId, orderDetailMsgDTO);
            // 违章
            setIllegal(orderId, orderDetailMsgDTO);
            // 主单价格日历
            setDailyPrice(orderId, orderDetailMsgDTO);
            // 证件信息
            setUserCertificate(orderId, orderDetailMsgDTO);
            // 取消违约金
            setPenaltyAmount(orderId, orderDetailMsgDTO);
            // 自助取还
            setSelfPrInfo(orderId, orderInfo, orderDetailMsgDTO);
            // 实际取还车时间
            setActualPickReturnDate(orderId, orderDetailMsgDTO);
            return orderDetailMsgDTO;
        } catch (Exception e) {
            log.error("获取订单详情失败, orderId={}", orderId, e);
            throw e;
        }
    }

    private void setActualPickReturnDate(Long orderId, OrderDetailMsgDTO orderDetailMsgDTO) {
        VehiclePickReturnExample pickReturnExample = new VehiclePickReturnExample();
        pickReturnExample.createCriteria().andDeletedEqualTo(YesOrNoEnum.NO.getValue()).andOrderIdEqualTo(orderId);
        List<VehiclePickReturn> pickReturnList = vehiclePickReturnSlaveMapper.selectByExample(pickReturnExample);
        if (CollectionUtils.isNotEmpty(pickReturnList)) {
            for (VehiclePickReturn pickReturn : pickReturnList) {
               if (VehiclePickReturnEnum.isPick(pickReturn.getPrType())) {
                   orderDetailMsgDTO.setActualPickupDate(DateUtil.getFormatDateStr(pickReturn.getPrTime(), DateUtil.yyyyMMddHHmmss));
               }
               if (VehiclePickReturnEnum.isReturnBack(pickReturn.getPrType())) {
                   orderDetailMsgDTO.setActualReturnDate(DateUtil.getFormatDateStr(pickReturn.getPrTime(), DateUtil.yyyyMMddHHmmss));
               }
            }
        }
    }

    public void pushOrderCharges(DeductionNotifyDTO deductionNotifyDTO, Long merchantId) {
        if (merchantId == null) {
            return;
        }
        orderPromiseExecutor.execute(() -> {
            Map<String, Object> map = new HashMap<>();
            map.put("merchantId",merchantId);
            map.put("deductionMsg", deductionNotifyDTO);
            openThirdService.msgPush(ORDER_DEDUCTION, merchantId, JSON.toJSONString(map));
        });
    }

    public void pushTransferContract(SyncContractResponse syncContractResponse, Long merchantId) {
        if (merchantId == null || syncContractResponse == null) {
            return;
        }
        asyncPromiseExecutor.execute(() -> {
            Map<String, Object> map = new HashMap<>();
            map.put("merchantId", merchantId);
            TransferContractMsgDTO transferContractMsgDTO = getTransferContractMsgDTO(syncContractResponse);
            map.put("transferContractMsg", transferContractMsgDTO);
            openThirdService.msgPush(ILLEGAL_TRANSFER_CONTRACT, merchantId, JSON.toJSONString(map));
            log.info("违章转移合同消息推送成功: merchantId={}, contractNo={}", merchantId, syncContractResponse.getContractNo());
        });
    }

    private TransferContractMsgDTO getTransferContractMsgDTO(SyncContractResponse syncContractResponse) {
        TransferContractMsgDTO transferContractMsgDTO = new TransferContractMsgDTO();
        
        // 手动映射字段
        transferContractMsgDTO.setOrderId(syncContractResponse.getOrderId());
        transferContractMsgDTO.setState(syncContractResponse.getState());
        transferContractMsgDTO.setMessage(syncContractResponse.getMessage());
        transferContractMsgDTO.setDriverPhone(syncContractResponse.getDriverPhone());
        transferContractMsgDTO.setEndTime(syncContractResponse.getEndTime());
        transferContractMsgDTO.setContractNo(syncContractResponse.getContractNo());
        transferContractMsgDTO.setPostMsg(syncContractResponse.getPostMsg());
        transferContractMsgDTO.setPostCode(syncContractResponse.getPostCode());
        
        return transferContractMsgDTO;
    }
}
