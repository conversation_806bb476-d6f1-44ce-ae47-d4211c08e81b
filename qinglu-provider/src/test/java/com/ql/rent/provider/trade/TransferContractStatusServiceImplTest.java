package com.ql.rent.provider.trade;

import com.alibaba.fastjson.JSON;
import com.ql.rent.AbstractTest;
import com.ql.rent.entity.trade.TransferContractStatus;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import org.junit.jupiter.api.Test;

import javax.annotation.Resource;

public class TransferContractStatusServiceImplTest extends AbstractTest {
    @Resource
    private ITransferContractStatusService transferContractStatusService;
    @Resource
    private TransferContractStatusServiceImpl transferContractStatusServiceImpl;

    @Test
    public void testPriceCalForThird() {
        TransferContractStatusVo result = transferContractStatusService.getLatestContract(44L, null, 467397L);
        System.out.println(JSON.toJSONString(result));
    }

//    @Test
//    public void retrySameContract() {
//        TransferContractStatus retrySameContract = new TransferContractStatus();
//        retrySameContract.setOrderId(1910786L);
//        retrySameContract.setVehicleId(118449L);
//        retrySameContract.setContractId("173200802231156750");
//        retrySameContract.setStatus((byte) -1);
//        retrySameContract.setPostCode("60001");
//        retrySameContract.setDriverName("赵俊武");
//        retrySameContract.setDriverPhone("***********");
//        retrySameContract.setDriverId("431102199606300033");
//        retrySameContract.setAccount("**************");
//        retrySameContract.setCarNumber("琼BD60537");
//        retrySameContract.setCarType("52");
//        retrySameContract.setContractNo("56dff5660c1c450e82b7f9dc63a31ae5");
//        retrySameContract.setBeginTime(DateUtil.getFormatDate("2024-11-20 20:46:00", "yyyy-MM-dd HH:mm:ss"));
//        retrySameContract.setEndTime(DateUtil.getFormatDate("2024-11-21 08:45:00", "yyyy-MM-dd HH:mm:ss"));
//        retrySameContract.setMerchantId(148L);
//        transferContractStatusServiceImpl.retrySameContract(retrySameContract, true);
//    }
}
