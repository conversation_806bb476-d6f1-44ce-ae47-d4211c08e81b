package com.ql.rent.api.trade;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.qinglusaas.logutil.QingluLogger;
import com.qinglusaas.logutil.QingluLoggerFactory;
import com.qinglusaas.logutil.constant.BusinessCode;
import com.ql.rent.aop.annotation.OrderPermissionVerif;
import com.ql.rent.api.excel.*;
import com.ql.rent.common.EasyExcelUtils;
import com.ql.rent.common.IRedisService;
import com.ql.rent.common.ITokenService;
import com.ql.rent.dto.trade.OrderPayUrlDTO;
import com.ql.rent.enums.EtcConstantsEnum;
import com.ql.rent.enums.trade.*;
import com.ql.rent.log.LogDomain;
import com.ql.rent.param.common.ApiConnBusiParam;
import com.ql.rent.param.trade.*;
import com.ql.rent.service.etc.EtcDeviceService;
import com.ql.rent.service.slave.IOrderSlaveService;
import com.ql.rent.service.store.IStoreInfoService;
import com.ql.rent.service.trade.*;
import com.ql.rent.share.exception.BizException;
import com.ql.rent.share.result.*;
import com.ql.rent.share.utils.DateUtil;
import com.ql.rent.share.utils.SpanEnhancer;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.bill.QueryPayResultVo;
import com.ql.rent.vo.etc.EtcOrderChargeVo;
import com.ql.rent.vo.etc.OrderCostDetailsVo;
import com.ql.rent.vo.price.InsuranceServicePriceVo;
import com.ql.rent.vo.store.StoreSimpleVo;
import com.ql.rent.vo.trade.*;
import com.ql.rent.vo.trade.third.*;
import com.ql.rent.vo.vehicle.StockVehicleBusyVO;
import com.ql.rent.vo.vehicle.VehicleInfoTagVO;
import com.ql.rent.vo.vehicle.VehicleTagVO;

import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.instrumentation.annotations.WithSpan;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.excel.write.style.column.LongestMatchColumnWidthStyleStrategy;

import java.net.URLEncoder;
import javax.servlet.ServletOutputStream;
import javax.validation.Valid;
import java.io.IOException;

/**
 * @auther musi
 * @date 2022/10/25 22:31
 */
@RestController
@Slf4j
@RequestMapping("/order")
@Api(tags = "订单相关接口")
@LogDomain(BusinessCode.ORDER)
public class OrderController {

    private final QingluLogger logger = QingluLoggerFactory.getLogger(OrderController.class);

    @Resource
    private ITokenService tokenService;

    @Resource
    private IOrderService orderService;

    @Resource
    private IThirdOrderService thirdOrderService;

    @Resource
    private IVehiclePickReturnService vehiclePickReturnService;

    @Resource
    private IOrderMemberService orderMemberService;

    @Resource
    private IOrderFixDataService orderFixDataService;

    @Resource
    private IOrderDepositService orderDepositService;

    @Resource
    private IOrderTagService orderTagService;

    @Resource
    private IStoreInfoService storeInfoService;

    @Resource
    private IRedisService redisService;
    @Resource
    private IOrderSlaveService orderSlaveService;
    private final static String ORDER_SLAVE_SWITCH = "order_slave_switch:";
    @Resource
    private EtcDeviceService etcDeviceService;

    @ApiOperation(value = "订单基础枚举类列表")
    @GetMapping("/v1/base/emun/list")
    public ResultMap<List<OrderBaseEnumVo>> getOrderBaseEmun(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            ApiConnBusiParam param = new ApiConnBusiParam();
            param.setMerchantId(loginVo.getMerchantId());
            Result<OrderBaseEnumVo> result = orderService.getOrderBaseEmun(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderBaseEmun", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单基础枚举类列表")
    @PostMapping("/v2/base/emun/list")
    public ResultMap<List<OrderBaseEnumVo>> getOrderBaseEmunPost(HttpServletRequest request,
                                                             @ApiParam("请求对象") @RequestBody ApiConnBusiParam param) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            if (param == null) {
                param = new ApiConnBusiParam();
            }
            param.setMerchantId(loginVo.getMerchantId());
            Result<OrderBaseEnumVo> result = orderService.getOrderBaseEmun(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderBaseEmun", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单服务算价")
    @PostMapping("/v1/price/calculate")
    public ResultMap<List<VehiclePriceAbbrVo>> priceCalculate(
            @RequestBody @ApiParam("车型服务算价信息") VehicleModelPriceCalQueryParam priceCalVo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            priceCalVo.setMerchantId(loginVo.getMerchantId());
            Result<List<VehiclePriceAbbrVo>> result = orderService.priceCalForSaas(priceCalVo, new HashMap<>());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController priceCalculate", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单创建")
    @PostMapping("/v1/create")
    @WithSpan("创建线下订单")
    public ResultMap<Long> create(@RequestBody @ApiParam("订单信息") CreateOrderVo createOrderVo,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withJson("订单信息", createOrderVo).withLong("车辆ID", createOrderVo.getVehicleId())
                    .withString("车牌号",
                            createOrderVo.getVehicleNo());
            LoginVo loginVo = tokenService.getUserByRequest(request);
            createOrderVo.setMerchantId(loginVo.getMerchantId());
            Result<Long> result = orderService.createOrder(createOrderVo, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController create", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "生成付款码")
    @PostMapping("/v1/get_pay_url")
    public ResultMap<OrderPayUrlDTO> getPayUrl(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("type") @ApiParam("付款码场景类型：1=创建订单，2=取车加购，3=续租，4=还车") Byte type,
            @RequestBody(required = false) PayFeeItemsVo payFeeItemsVo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            if (payFeeItemsVo != null) {
                payFeeItemsVo.setUserId(loginVo.getUserId());
            } else {
                payFeeItemsVo = new PayFeeItemsVo();
                payFeeItemsVo.setUserId(loginVo.getUserId());
            }
            Result<OrderPayUrlDTO> result = orderService.getPayUrl(orderId, type, payFeeItemsVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getPayUrl", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消订单支付")
    @PostMapping("/v1/cancel_order_pay")
    public ResultMap<Boolean> cancelOrderPay(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("payNo") @ApiParam("支付号") String payNo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.cancelOrderPay(orderId, payNo, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController cancelOrderPay", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "轮询查询付款结果")
    @PostMapping("/v1/query_pay_result")
    public ResultMap<QueryPayResultVo> queryPayResult(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("payNo") @ApiParam("支付号") String payNo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<QueryPayResultVo> result = orderService.queryPayResult(orderId, payNo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getPayUrl", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消服务项")
    @PostMapping("/v1/cancel_service")
    public ResultMap<Boolean> cancelService(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("itemType") @ApiParam("服务项类型：0=订单费用项，2=还车费用项") Byte type,
            @RequestParam("itemId") @ApiParam("服务项ID") Long itemId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.cancelService(orderId, type, itemId, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getPayUrl", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单调价")
    @PostMapping("/v1/adjust_amount")
    public ResultMap<Long> adjustOrderAmount(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("discountType") @ApiParam("折扣方式：0：整单优惠；1：补充金额") Byte discountType,
            @RequestParam("discountAmount") @ApiParam("折扣金额") Integer discountAmount,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.adjustOrderAmount(orderId, discountType, discountAmount,
                    loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController adjustOrderAmount", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单列表")
    @PostMapping("/v1/list")
    public ResultMap<OrderInfoListVo> list(@RequestBody @ApiParam("订单列表查询条件") OrderInfoParam orderInfoParam,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            log.info("查询订单列表, merchantId={}, userId={}, request={}",
                    loginVo.getMerchantId(), loginVo.getUserId(), JSON.toJSONString(orderInfoParam));
            String rdkey = "orderList:" + loginVo.getMerchantId() + "_" + loginVo.getUserId();
            long check = redisService.setnx(rdkey, 100L, TimeUnit.MILLISECONDS);
            if (check > 1) {
                if (check > 3) {
                    log.info("查询订单列表, 触发限流, 缓存重置, rdkey={}, check={}", rdkey, check);
                    redisService.remove(rdkey);
                }
                log.info("查询订单列表, 请勿频繁操作, rdkey={}， check={}", rdkey, check);
                return ApiResultUtil.failResult("请勿频繁操作");
            }
            orderInfoParam.setMerchantId(loginVo.getMerchantId());
            if (loginVo.getIsDiver()) {
                log.info("OrderController isDiver list");
                orderInfoParam.setDriverUserId(loginVo.getUserId());
            }
            Result<List<StoreSimpleVo>> storeListResult = storeInfoService.storeSampleForSelect(loginVo, null);
            log.info("查询订单列表, 查询用户有权限门店, loginVo={}, storeListResult={}", JSON.toJSONString(loginVo),
                    JSON.toJSONString(storeListResult));
            if (ResultUtil.isResultNotSuccess(storeListResult)) {
                return ApiResultUtil.failResult(storeListResult.getMessage());
            }
            if (CollectionUtils.isEmpty(storeListResult.getModel())) {
                log.info("查询订单列表, 用户没有任何门店权限, loginVo={}", JSON.toJSONString(loginVo));
                return ApiResultUtil.failResult("该用户没有分配门店权限");
            }
            List<Long> storeIdList = storeListResult.getModel().stream().map(item -> item.getStoreId())
                    .collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(storeIdList)) {
                if (CollectionUtils.isEmpty(orderInfoParam.getStoreIdList())) {
                    log.info("查询订单列表, 查询条件门店为空, 设置用户门店, storeIdList={}", JSON.toJSONString(storeIdList));
                    orderInfoParam.setStoreIdList(storeIdList);
                } else {
                    log.info("查询订单列表, 用户门店storeIdList={}, 查询条件门店={}", JSON.toJSONString(storeIdList),
                            JSON.toJSONString(orderInfoParam.getStoreIdList()));
                    List<Long> newStoreIdList = new ArrayList<>();
                    for (Long storeId : storeIdList) {
                        if (orderInfoParam.getStoreIdList().contains(storeId)) {
                            newStoreIdList.add(storeId);
                        }
                    }
                    log.info("查询订单列表, 去重后门店={}", JSON.toJSONString(newStoreIdList));
                    if (CollectionUtils.isEmpty(newStoreIdList)) {
                        return ApiResultUtil.failResult("该用户没有分配门店权限");
                    }
                    orderInfoParam.setStoreIdList(newStoreIdList);
                }
            }
            Object obj = redisService.get(ORDER_SLAVE_SWITCH);
            log.info("查询订单列表, 订单备库开关, obj={}", JSON.toJSONString(obj));
            Result<OrderInfoListVo> result;
            if (obj != null) {
                String switchStr = String.valueOf(obj);
                if (switchStr.contains(String.valueOf(loginVo.getMerchantId()))
                        || Objects.equals("all", switchStr)) {
                    result = orderSlaveService.getOrderList(orderInfoParam, loginVo);
                } else {
                    result = orderService.getOrderList(orderInfoParam, loginVo);
                }
            } else {
                result = orderService.getOrderList(orderInfoParam, loginVo);
            }
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            // List<OrderInfoVo> orderInfoVos =
            // orderService.getRPAOrderList(result.getModel().getCurrentList(), loginVo);
            // result.getModel().setCurrentList(orderInfoVos);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("查询订单列表异常", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询RPA订单")
    @PostMapping("/v1/rpa-order-detail")
    @OrderPermissionVerif
    public ResultMap<RPAOrderInfoVo> getRPAOrderInfo(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<OrderDetailVo> result = orderService.getOrderDetail(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            OrderDetailOrderInfoVo orderInfoVo = result.getModel().getOrderInfoVo();
            RPAOrderInfoVo rpaOrderInfoVo = orderService.getRPAOrder(orderInfoVo, loginVo);
            return ApiResultUtil.successResult(rpaOrderInfoVo);
        } catch (Exception e) {
            log.error("查询RPA订单错误", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单详细")
    @PostMapping("/v1/detail")
    @OrderPermissionVerif
    public ResultMap<OrderDetailVo> detail(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            // LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<OrderDetailVo> result = orderService.getOrderDetail(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController detail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "长订单详细")
    @PostMapping("/v1/long_detail")
    public ResultMap<LongOrderDetailVo> longDetail(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<LongOrderDetailVo> result = orderService.getLongOrderDetail(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController long_detail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "更新长订单押金")
    @PostMapping("/v1/update_longorder_deposit")
    public ResultMap<Boolean> updateLongOrderDeposit(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("type") @ApiParam("押金类型：0：租车押金；1：违章押金") Byte type,
            @RequestParam("amount") @ApiParam("金额") Integer amount,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.updateLongOrderDeposit(orderId, type, amount, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController updateLongOrderDeposit", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消订单")
    @PostMapping("/v1/cancel")
    @WithSpan("取消订单")
    public ResultMap<Boolean> cancelOrder(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("reduceAmount") @ApiParam("减免的违约金") Integer reduceAmount,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("订单号", orderId)
                    .withInt("减免的违约金", reduceAmount);
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.cancelOrder(orderId, reduceAmount, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController cancelOrder", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "违约金退款")
    @PostMapping("/v1/penalty_refund")
    public ResultMap<Boolean> penaltyRefund(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("amount") @ApiParam("退款金额") Integer amount,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.penaltyRefund(orderId, amount, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController penaltyRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "续租记录")
    @PostMapping("/v1/rerent/list")
    public ResultMap<List<RerentOrderVo>> getRerentOrderList(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<RerentOrderVo>> result = orderService.getRerentOrderList(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getRerentOrderList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "续租订单服务项")
    @PostMapping("/v1/rerent/service_list")
    public ResultMap<List<ServiceItemAmountVo>> getRerentServiceList(
            @RequestParam("orderId") @ApiParam("续租订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<ServiceItemAmountVo>> result = orderService.getRerentServiceList(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getRerentServiceList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "续租的预计价")
    @PostMapping("/v1/rerent/pre_calculate")
    public ResultMap<RerentPrecalculateVo> rerentPrecalculate(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("rerentTime") @ApiParam("续租时间") Long rerentTime,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<RerentPrecalculateVo> result = orderService.rerentPrecalculateForSaas(orderId, rerentTime);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController cancelRule", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "确认续租(创建续租单)")
    @PostMapping("/v1/rerent/create")
    @WithSpan("创建线下续租订单")
    public ResultMap<Long> createRerentOrder(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("rerentTime") @ApiParam("续租时间") Long rerentTime,
            @RequestParam("forcibly") @ApiParam("是否强制续租，无冲突为false") boolean forcibly,
            @RequestParam("reduceType") @ApiParam("减免类型") Byte reduceType,
            @RequestParam("reduceAmount") @ApiParam("减免金额") Integer reduceAmount,
            @RequestParam("payKind") @ApiParam("支付方式，1，支付宝付款码；99，其他") Integer payKind,
                                             @RequestParam(value = "syncThird", required = false) @ApiParam("减免金额") boolean syncThird,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("订单号", orderId)
                    .withLong("续租时间", rerentTime)
                    .withBoolean("是否强制续租", forcibly)
                    .withByte("减免类型", reduceType)
                    .withInt("减免金额", reduceAmount);
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Long> result = orderService.createRerentOrder(orderId, rerentTime, forcibly, reduceType, false,
                    reduceAmount, loginVo.getUserId(), payKind, syncThird);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController createRerentOrder", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消续租单")
    @PostMapping("/v1/rerent/cancel")
    @WithSpan("取消线下续租订单")
    public ResultMap<Boolean> cancelRerentOrder(
            @RequestParam("orderId") @ApiParam("续租订单id") Long orderId,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("续租订单号", orderId);
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.cancelRerentOrder(orderId, loginVo.getUserId(), false);
            if (ResultUtil.isResultNotSuccess(result)) {
                span.setStatus(StatusCode.ERROR, result.getMessage());
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController cancelRerentOrder", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消政策")
    @PostMapping("/v1/cancel_rule")
    public ResultMap<List<OrderCancelRuleVo>> cancelRule(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<OrderCancelRuleVo>> result = orderService.getCancelRule(orderId, null, null, null, false);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController cancelRule", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单账单明细")
    @PostMapping("/v1/bill_detail")
    public ResultMap<OrderBillDetailVo> getOrderBillDetail(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<OrderBillDetailVo> result = orderService.getOrderBillDetail(orderId, null);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            OrderBillDetailVo orderBill = result.getModel();
            // 仅有SaaS用户和商家的管理员、财务 可以查看数据
            if (!loginVo.isSaasAdmin() && !Boolean.TRUE.equals(loginVo.getIsFinance())
                    && !Boolean.TRUE.equals(loginVo.isStoreAdmin())) {
                orderBill.setOrderReconciliation(null);
            }
            return ApiResultUtil.successResult(orderBill);
        } catch (Exception e) {
            log.error("OrderController getOrderBillDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单取还车信息")
    @PostMapping("/v1/pick_retuen")
    public ResultMap<OrderPickReturnVO> getOrderPickReturn(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            tokenService.getUserByRequest(request);
            Result<OrderPickReturnVO> result = vehiclePickReturnService.getOrderPickReturn(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderPickReturn", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "违章记录")
    @PostMapping("/v1/illegal_order")
    public ResultMap<List<VehicleIllegalOrderVO>> getIllegalOrder(
            @RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<VehicleIllegalOrderVO>> result = orderService.getIllegalOrder(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderPickReturn", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "车损记录")
    @PostMapping("/v1/damage_order")
    public ResultMap<List<VehicleDamageOrderVO>> getDamageOrder(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<VehicleDamageOrderVO>> result = orderService.getDamageOrder(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getDamageOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "押金政策")
    @PostMapping("/v1/deposit_policy")
    public ResultMap<OrderDepositPolicyVo> getDepositPolicy(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<OrderDepositPolicyVo> result = orderService.getDepositPolicy(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getDepositPolicy", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "获取取车押金")
    @PostMapping("/v1/pickup_deposit")
    public ResultMap<PickupOrderDepositVo> getPickupDeposit(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<PickupOrderDepositVo> result = orderService.getPickupDeposit(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getPickupDeposit", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "保险服务明细")
    @PostMapping("/v1/service_detail")
    public ResultMap<InsuranceServicePriceVo> getServiceDetail(
            @RequestParam("serviceId") @ApiParam("服务项id") Long serviceId,
            @RequestParam(value = "orderId", required = false) @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<InsuranceServicePriceVo> result = orderService.getServiceDetail(orderId, serviceId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getDepositPolicy", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "新增订单备注")
    @PostMapping("/v1/remark/save")
    public ResultMap<Boolean> save(@RequestBody OrderRemarkParam orderRemarkParam,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.saveOrderRemark(orderRemarkParam.getOrderId(),
                    orderRemarkParam.getRemarkId(),
                    orderRemarkParam.getRemark(), orderRemarkParam.getUrlList(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController remarkSave", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 保存订单 - 违章记录
     */
    @ApiOperation(value = "备注列表")
    @PostMapping("/v1/remark/list")
    public ResultMap<List<OrderRemarkVo>> remarkList(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<OrderRemarkVo>> result = orderService.getOrderRemarkList(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController remarkList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "改排")
    @PostMapping("/v1/plan_vehicle/update")
    @WithSpan("库存改排")
    public ResultMap<Boolean> updatePlanVehicle(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("vehicleModelId") @ApiParam("车型id") Long vehicleModelId,
            @RequestParam("vehicleId") @ApiParam("车辆id") Long vehicleId,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("订单ID", orderId).withLong("车型ID", vehicleModelId).withLong("车辆ID",
                    vehicleId);
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.updatePlanVehicle(orderId, vehicleId, vehicleModelId, loginVo, 1);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController updatePlanVeicle", e);
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "强制改排 2023-07-08 新增")
    @PostMapping("/v1/plan_vehicle/force_update")
    @WithSpan("强制库存改排")
    public ResultMap<Boolean> forceUpdatePlanVehicle(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("vehicleId") @ApiParam("车辆id") Long vehicleId,
            HttpServletRequest request) {
        Span span = Span.current();
        try {
            SpanEnhancer.of(span).withLong("订单ID", orderId).withLong("车辆ID",
                    vehicleId);
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.forceUpdatePlanVehicle(orderId, vehicleId, loginVo, 1);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            span.setStatus(StatusCode.OK);
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            span.recordException(e);
            span.setStatus(StatusCode.ERROR);
            log.error("OrderController forceUpdatePlanVehicle", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询订单排车的可用车辆")
    @PostMapping("/v1/available_vehicle/list")
    public ResultMap<Boolean> listOrderAvailableVehicle(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam(value = "vehicleModelId", required = false) @ApiParam("车型id") Long vehicleModelId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<VehicleInfoTagVO>> result = vehiclePickReturnService.listOrderAvailableVehicle(orderId,
                    vehicleModelId, loginVo.getLoginName(), loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController listOrderAvailableVehicle", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "排车接口")
    @PostMapping("/v1/plan_vehicle/save")
    public ResultMap<Boolean> savePlanVehicle(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("vehicleModelId") @ApiParam("车型id") Long vehicleModelId,
            @RequestParam("vehicleId") @ApiParam("车辆id") Long vehicleId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> result = orderService.savePlanVehicle(orderId, vehicleId, vehicleModelId,
                    loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController savePlanVehicle", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询选择车辆的库存冲突情况")
    @PostMapping("/v1/list_conflict_stock")
    public ResultMap<List<StockVehicleBusyVO>> listConflictBusyVehicle(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("vehicleId") @ApiParam("车辆id") Long vehicleId,
            HttpServletRequest request) {
        try {
            // 保险其间 return failResult
            return ApiResultUtil.failResult("");
        } catch (Exception e) {
            log.error("OrderController listConflictBusyVehicle", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "更新用户证件信息")
    @PostMapping("/v1/certificate/update")
    public ResultMap<Boolean> updateUserCertificate(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestBody @ApiParam("用户证件信息") UserCertificateVo userCertificateVo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderMemberService.updateUserCertificate(userCertificateVo, orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController updateUserCertificate", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "用户证件信息")
    @PostMapping("/v1/certificate/detail")
    public ResultMap<UserCertificateVo> getUserCertificate(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<UserCertificateVo> result = orderMemberService.getUserCertificate(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getUserCertificate", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "更新订单状态")
    @PostMapping("/v1/update_status")
    public ResultMap<Boolean> updateOrderStatus(@RequestParam("orderId") @ApiParam("订单id") Long orderId,
            @RequestParam("status") @ApiParam("订单状态:0:未提交;1:已提交;2:确认中;3:已确认;4:已排车;5:已取车;6:已还车;7:取消中;8:已取消") Byte status,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.updateOrderStatus(orderId, status, loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController updateOrderStatus", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单Excel下载", notes = "订单Excel下载")
    @PostMapping("/v1/downExcel")
    public ResultMap downFile(HttpServletRequest request,
            HttpServletResponse response,
            @RequestBody OrderInfoParam orderInfoParam) {
        ServletOutputStream out = null;
        ExcelWriter excelWriter = null;
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            orderInfoParam.setMerchantId(loginVo.getMerchantId());

            // 限制最大导出数量
            final int MAX_EXPORT_SIZE = 10000;
            final int PAGE_SIZE = 500;
            logger.startLog().businessCode(BusinessCode.ORDER_EXPORT).with("orderInfoParam", orderInfoParam)
                .with("loginVo", loginVo).log("订单导出");
            // Redis防重复提交
            String rdkey = "orderImport:" + loginVo.getMerchantId() + "_" + loginVo.getUserId();
            long check = redisService.setnx(rdkey, 30L);
            if (check > 1) {
                return ApiResultUtil.failResult("请勿频繁操作");
            }

            // 权限校验
            if (loginVo.getIsDiver()) {
                orderInfoParam.setDriverUserId(loginVo.getUserId());
            }
            Result<List<StoreSimpleVo>> storeListResult = storeInfoService.storeSampleForSelect(loginVo, null);
            if (ResultUtil.isResultNotSuccess(storeListResult)) {
                return ApiResultUtil.failResult(storeListResult.getMessage());
            }
            if (CollectionUtils.isEmpty(storeListResult.getModel())) {
                return ApiResultUtil.failResult("该用户没有分配门店权限");
            }

            // 处理门店权限
            List<Long> storeIdList = storeListResult.getModel().stream()
                    .map(StoreSimpleVo::getStoreId)
                    .collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(storeIdList)) {
                if (CollectionUtils.isEmpty(orderInfoParam.getStoreIdList())) {
                    orderInfoParam.setStoreIdList(storeIdList);
                } else {
                    List<Long> newStoreIdList = orderInfoParam.getStoreIdList().stream()
                            .filter(storeIdList::contains)
                            .collect(Collectors.toList());
                    if (CollectionUtils.isEmpty(newStoreIdList)) {
                        return ApiResultUtil.failResult("该用户没有分配门店权限");
                    }
                    orderInfoParam.setStoreIdList(newStoreIdList);
                }
            }

            // 设置响应头
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setCharacterEncoding("utf-8");
            String fileName = URLEncoder.encode("订单列表", StandardCharsets.UTF_8).replaceAll("\\+", "%20");
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + fileName + ".xlsx");
            out = response.getOutputStream();
            // 使用EasyExcel流式写入
             excelWriter = EasyExcel.write(out)
                .registerWriteHandler(EasyExcelUtils.defaultStyles())         // 注册样式策略
                .registerWriteHandler(new LongestMatchColumnWidthStyleStrategy())   // 注册列宽策略
                .build();

            WriteSheet writeSheet = EasyExcel.writerSheet("订单列表")
                .head(OrderExcelBean.class)
                .build();

            
            // 分页查询处理
            int pageIndex = 1;
            int total = 0;

            while (true) {
                orderInfoParam.setPageSize(PAGE_SIZE);
                orderInfoParam.setPageIndex(pageIndex);
                orderInfoParam.setNeedCount(false);

                // 查询订单数据
                Result<OrderInfoListVo> result;
                Object obj = redisService.get(ORDER_SLAVE_SWITCH);
                if (obj != null) {
                    String switchStr = String.valueOf(obj);
                    if (switchStr.contains(String.valueOf(loginVo.getMerchantId()))
                            || Objects.equals("all", switchStr)) {
                        result = orderSlaveService.getOrderList(orderInfoParam, loginVo);
                    } else {
                        result = orderService.getOrderList(orderInfoParam, loginVo);
                    }
                } else {
                    result = orderService.getOrderList(orderInfoParam, loginVo);
                }

                if (!result.isSuccess() || CollectionUtils.isEmpty(result.getModel().getCurrentList())) {
                    break;
                }

                List<OrderBaseExcelBean> pageList = new ArrayList<>();

                // 处理订单数据
                if (orderInfoParam.getOrderType() == null || orderInfoParam.getOrderType() == 0) {
                    Result<Map<Long, OrderFeeVo>> mapResult = orderService
                            .getOrderExcelList(result.getModel().getCurrentList());

                    for (OrderInfoVo vo : result.getModel().getCurrentList()) {
                        OrderExcelBean bean = new OrderExcelBean();
                        buildOrderExcelBean(vo, bean);
                        processOrderExcelBean(vo, bean, mapResult.getModel().get(vo.getId()));
                        pageList.add(bean);
                    }
                } else {
                    for (OrderInfoVo vo : result.getModel().getCurrentList()) {
                        LongOrderExcelBean bean = new LongOrderExcelBean();
                        buildLongOrderExcelBean(vo, bean);
                        bean.setPayAmount(bean.getPayAmount() / 100);
                        bean.setReceivableAmount(bean.getReceivableAmount() / 100);
                        pageList.add(bean);
                    }
                }

                // 写入当前页数据
                excelWriter.write(pageList, writeSheet);

                total += pageList.size();
                if (total >= MAX_EXPORT_SIZE) {
                    break;
                }

                pageIndex++;
                pageList.clear();
            }

            return null;

        } catch (Exception e) {
            logger.startLog().businessCode(BusinessCode.ORDER_EXPORT).logError("订单导出异常", e);
            return ApiResultUtil.failResult("订单导出异常");
        }finally{
            if(excelWriter != null){
                excelWriter.finish();
                excelWriter.close();
            }
        
            if(out != null){
                try {
                    out.close();
                } catch (IOException e) {
                    log.error("订单导出异常: {}", e.getMessage());
                }
            }
        }
    }
    

    // 处理订单Excel数据
    private void processOrderExcelBean(OrderInfoVo vo, OrderExcelBean bean, OrderFeeVo orderFeeVo) {
        if (vo.getActualPickupDate() != null) {
            bean.setActualPickupDate(DateUtil.getFormatDateStr(new Date(vo.getActualPickupDate()),
                    DateUtil.yyyyMMddHHmmss));
        }
        if (vo.getActualReturnDate() != null) {
            bean.setActualReturnDate(DateUtil.getFormatDateStr(new Date(vo.getActualReturnDate()),
                    DateUtil.yyyyMMddHHmmss));
        }

        if (orderFeeVo != null) {
            bean.setSettlementStatus(FinanceEnum.ReconciliationSettlementStatusEnum
                    .getName(orderFeeVo.getSettlementStatus()));
            BeanUtils.copyProperties(orderFeeVo, bean);
        }

        if (vo.getActualPickupDate() != null && vo.getActualReturnDate() != null) {
            bean.setActualRentalPeriod(
                    DateUtil.getDatePoorHours(vo.getActualPickupDate(), vo.getActualReturnDate()));
        }

        if (vo.getIsReptile() != null && vo.getIsReptile() == 1
                && StringUtils.isNotBlank(bean.getOrderSource())) {
            bean.setOrderSource(bean.getOrderSource() + "同步");
        }

        processCardOrderInfo(vo, bean);

        if (vo.getOrderExtraVo() != null && vo.getOrderExtraVo().getNoWorried() != null
                && vo.getOrderExtraVo().getNoWorried() == 1) {
            bean.setBuyItNowPrice("是");
        } else {
            bean.setBuyItNowPrice("否");
        }
    }

    // 处理卡订单信息
    private void processCardOrderInfo(OrderInfoVo vo, OrderExcelBean bean) {
        if (vo.getCardOrderExt() != null) {
            if (Objects.equals("1", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("次卡");
            } else if (Objects.equals("2", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("天卡");
            } else if (Objects.equals("3", vo.getCardOrderExt().getCardTag())) {
                bean.setCardTag("随心租卡");
            }
            bean.setCardOrderId(vo.getCardOrderExt().getCardOrderId());
            bean.setCardId(vo.getCardOrderExt().getCardId());
            bean.setCardTradeNo(vo.getCardOrderExt().getCardTradeNo());
            bean.setBasicRentFee(vo.getCardOrderExt().getBasicRentFee());
            bean.setBasicServiceFee(vo.getCardOrderExt().getBasicServiceFee());
            bean.setPoundageFee(vo.getCardOrderExt().getPoundageFee());
        }
    }

    private void buildLongOrderExcelBean(OrderInfoVo vo, LongOrderExcelBean bean) {
        bean.setPayAmount(vo.getPayAmount());
        bean.setReceivableAmount(vo.getReceivableAmount());
        buildOrderBaseExcelBean(vo, bean);
    }

    private void buildOrderExcelBean(OrderInfoVo vo, OrderExcelBean bean) {
        bean.setActualRentalPeriod(bean.getActualRentalPeriod());
        bean.setRerentCount(vo.getRerentCount());
        bean.setPickupOilLiter(vo.getPickupOilLiter());
        bean.setReturnOilLiter(vo.getReturnOilLiter());
        bean.setPickupMileage(vo.getPickupMileage());
        bean.setReturnMileage(vo.getReturnMileage());
        bean.setOrgVehicleModelName(vo.getOrgVehicleModelName());
        buildOrderBaseExcelBean(vo, bean);
    }

    /**
     * 临时处理成public static，使其能被外部引用。
     */
    private void buildOrderBaseExcelBean(OrderInfoVo vo, OrderBaseExcelBean bean) {
        bean.setId(vo.getId());
        bean.setOrderNo(vo.getOrderNo());
        bean.setVehicleId(vo.getVehicleId());
        bean.setVehicleName(vo.getVehicleName());
        bean.setVehicleNo(vo.getVehicleNo());
        bean.setRentalPeriod(vo.getRentalPeriod());
        bean.setPickupAddrTypeStr(vo.getPickupAddrTypeStr());
        bean.setReturnAddrTypeStr(vo.getReturnAddrTypeStr());
        bean.setPickupStoreName(vo.getPickupStoreName());
        bean.setPickupCity(vo.getPickupCity());
        bean.setPickupAddr(vo.getPickupAddr());
        bean.setReturnStoreName(vo.getReturnStoreName());
        bean.setReturnCity(vo.getReturnCity());
        bean.setReturnAddr(vo.getReturnAddr());
        bean.setPickupDriver(vo.getPickupDriver());
        bean.setReturnDriver(vo.getReturnDriver());
        bean.setUserName(vo.getUserName());
        bean.setMobile(vo.getMobile());
        bean.setSourceOrderId(vo.getSourceOrderId());

        bean.setOrderTime(DateUtil.getFormatDateStr(new Date(vo.getOrderTime()), DateUtil.yyyyMMddHHmmss));
        bean.setOrderSource(OrderSourceEnum.getNameByStatus(vo.getOrderSource()));
        bean.setOrderStatus(OrderStatusEnum.getByStatus(vo.getOrderStatus()).getName());
        bean.setPickupDate(DateUtil.getFormatDateStr(vo.getPickupDate(), DateUtil.yyyyMMddHHmmss));
        bean.setReturnDate(DateUtil.getFormatDateStr(vo.getReturnDate(), DateUtil.yyyyMMddHHmmss));
        bean.setFreeDepositDegree(
                FreeDepositTypeEnum.getNameByType(Byte.valueOf(String.valueOf(vo.getFreeDepositDegree()))));
        bean.setTagList(CollectionUtils.isEmpty(vo.getTagList()) ? ""
                : StringUtils.join(
                        vo.getTagList().stream().map(VehicleTagVO::getTagName).collect(Collectors.toList()).toArray(),
                        ","));
        bean.setOrderTagList(
                CollectionUtils.isEmpty(vo.getOrderTagList()) ? "" : StringUtils.join(vo.getOrderTagList(), ","));

        if (vo.getVehicleModel() != null && StringUtils.isNotBlank(
                vo.getVehicleModel().getVehicleUnionName())) {
            bean.setVehicleName(vo.getVehicleModel().getVehicleUnionName());
        }
        if (vo.getPickupDate() != null && vo.getReturnDate() != null) {
            bean.setRentalPeriod(DateUtil.getDatePoorHours(vo.getPickupDate().getTime(), vo.getReturnDate().getTime()));
        }
    }

    @ApiOperation(value = "商家全部服务列表（新）")
    @PostMapping("/v1/service/all")
    public ResultMap<ServiceItemListV2Vo> getServiceAll(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<ServiceItemListV2Vo> result = orderService.getServiceAll(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("VehicleBrandController getServiceAll", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "算价（新）")
    @PostMapping("/v1/price/new_calculate")
    public ResultMap<List<VehiclePriceAbbrVo>> newPriceCalculate(
            @RequestBody @ApiParam("车型服务算价信息") VehicleModelPriceCalQueryV2Param priceCalVo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            priceCalVo.setMerchantId(loginVo.getMerchantId());
            Result<List<VehiclePriceAbbrVo>> result = orderService.newPriceCalForSaas(priceCalVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController priceCalculate", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单下拉框")
    @PostMapping("/v1/base_list")
    public ResultMap<List<OrderInfoVo>> getOrderBaseList(@RequestBody OrderInfoParam orderInfoParam,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            orderInfoParam.setMerchantId(loginVo.getMerchantId());

            Object obj = redisService.get(ORDER_SLAVE_SWITCH);
            Result<List<OrderInfoVo>> result;
            if (obj != null) {
                String switchStr = String.valueOf(obj);
                if (switchStr.contains(String.valueOf(loginVo.getMerchantId()))
                        || Objects.equals("all", switchStr)) {
                    result = orderSlaveService.getOrderBaseList(orderInfoParam, loginVo);
                } else {
                    result = orderService.getOrderBaseList(orderInfoParam, loginVo);
                }
            } else {
                result = orderService.getOrderBaseList(orderInfoParam, loginVo);
            }
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderBaseList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "生成长订单初始分期列表")
    @PostMapping("/v1/init_instalment")
    public ResultMap<List<OrderInstalmentVo>> initInstalment(
            @RequestBody @ApiParam("生成分期信息") OrderInstalmentConfigVo orderInstalmentConfigVo,
            HttpServletRequest request) {
        try {
            Result<List<OrderInstalmentVo>> result = orderService.initInstalment(orderInstalmentConfigVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController initInstalment", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "长订单分期计划")
    @PostMapping("/v1/instalment_list")
    public ResultMap<List<OrderInstalmentVo>> instalmentList(@RequestParam("orderId") @ApiParam("长订单ID") Long orderId,
            HttpServletRequest request) {
        try {
            Result<List<OrderInstalmentVo>> result = orderService.instalmentList(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController instalmentList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "获取分期付款页面数据")
    @PostMapping("/v1/get_instalment_pay")
    public ResultMap<OrderInstalmentVo> getInstalmentPay(@RequestParam("orderId") @ApiParam("长订单ID") Long orderId,
            HttpServletRequest request) {
        try {
            Result<OrderInstalmentVo> result = orderService.getInstalmentPay(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getInstalmentPay", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "保存分期付款支付")
    @PostMapping("/v1/save_instalment_pay")
    public ResultMap<Boolean> saveInstalmentPay(@RequestParam("orderId") @ApiParam("长订单ID") Long orderId,
            @RequestParam("instalmentId") @ApiParam("分期ID") Long instalmentId,
            @RequestParam("payAmount") @ApiParam("付款金额") Integer payAmount,
            @RequestParam("remark") @ApiParam("备注") String remark,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.saveInstalmentPay(orderId, instalmentId, payAmount, remark,
                    loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController saveInstalmentPay", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "所有订单自定义标签")
    @PostMapping("/v1/order_tag/all")
    public ResultMap<List<OrderTagVO>> getAllOrderTag(HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<OrderTagVO>> result = orderTagService.getAllOrderTag(loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getAllOrderTag", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "查询标签关联的订单列表")
    @PostMapping("/v1/order_tag/relation_list")
    public ResultMap<List<Long>> getTagRelationList(@RequestParam("tagId") @ApiParam("标签ID") Long tagId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<Long>> result = orderTagService.getTagRelationList(tagId, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getTagRelationList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "删除")
    @PostMapping("/v1/order_tag/del")
    public ResultMap<Boolean> delOrderTag(@RequestParam("tagId") @ApiParam("标签ID") Long tagId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderTagService.delOrderTag(tagId, loginVo.getMerchantId(), loginVo.getUserId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController delOrderTag", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单自定义标签列表")
    @PostMapping("/v1/order_tag/list")
    public ResultMap<List<OrderTagVO>> getOrderTagList(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<List<OrderTagVO>> result = orderTagService.getOrderTagList(orderId, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderTagList", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "保存订单标签")
    @PostMapping("/v1/order_tag/save")
    public ResultMap<Boolean> saveOrderTag(@RequestParam("tag") @ApiParam("要保存的标签") String tag,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderTagService.saveOrderTag(tag, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController saveOrderTag", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "保存订单标签关联（保存勾选）")
    @PostMapping("/v1/order_tag_relation/save")
    public ResultMap<Boolean> saveOrderTagRelation(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            @RequestParam("tag") @ApiParam("要保存的标签") List<Long> tagIdList,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderTagService.saveOrderTagRelation(orderId, tagIdList, loginVo.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController saveOrderTagRelation", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修复数据接口")
    @PostMapping("/v1/fix_data")
    public ResultMap<Boolean> fixOrderSnapshot(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            @RequestParam("vehicleId") @ApiParam("车辆ID") Long vehicleId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            // Result<Boolean> result = orderFixDataService.fixOrderSnapshot(orderId,
            // vehicleId);
            Result<Boolean> result = orderFixDataService.delOrderSnapshot();
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController fixData", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修复订单快照接口")
    @PostMapping("/v1/add_order_snapshot")
    public ResultMap<Boolean> addOrderSnapshot(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderFixDataService.addOrderSnapshot(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController addOrderSnapshot", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "同步渠道订单")
    @PostMapping("/v1/sync_channel_order")
    public ResultMap<Boolean> syncChannelOrder(@RequestParam("merchantId") @ApiParam("商家ID") Long merchantId,
                                             @RequestParam("channelId") @ApiParam("渠道ID") Long channelId,
                                             @RequestParam("sourceOrderId") @ApiParam("订单ID") String sourceOrderId,
            HttpServletRequest request) {
        try {
            // LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderFixDataService.syncChannelOrder(merchantId, channelId, sourceOrderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController syncChannelOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "订单消息推送")
    @PostMapping("/v1/order_msg_push")
    public ResultMap<Boolean> orderMsgPush(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
                                           HttpServletRequest request) {
        try {
            // LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderFixDataService.orderMsgPush(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController orderMsgPush", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "导入订单excel脚本")
    @PostMapping("/v1/import/excel")
    public ResultMap importExcel(MultipartFile file, @RequestParam("type") @ApiParam("excel类型") Integer type,
            @RequestParam("merchantId") @ApiParam("商家ID") Long merchantId,
            HttpServletRequest request, HttpServletResponse response) {
        try {
            log.info("导入订单excel脚本开始");
            if (type == null) {
                return ApiResultUtil.successResult(null);
            }

            if (type == 1) {
                List<OrderBaseExcelBean> list = new ArrayList<>();

                OrderExcelListener orderExcelListener = new OrderExcelListener();
                EasyExcel.read(file.getInputStream(), OrderImportExcelBean.class, orderExcelListener).sheet().doRead();
                List<OrderImportExcelBean> importList = orderExcelListener.getList();
                log.info("导入订单excel脚本, excel数据={}", JSON.toJSONString(importList));
                if (CollectionUtils.isEmpty(importList)) {
                    return ApiResultUtil.successResult(null);
                }

                // List<OrderImportExcelBean> importList =
                // EasyExcel.read(file.getInputStream()).head(OrderImportExcelBean.class).sheet().headRowNumber(0).doReadSync();

                log.info("导入订单excel脚本, excel数据={}", JSON.toJSONString(importList));
                if (CollectionUtils.isEmpty(importList)) {
                    return ApiResultUtil.successResult(null);
                }

                List<String> sourceOrderIds = importList.stream().map(OrderImportExcelBean::getSourceOrderId)
                        .collect(Collectors.toList());

                LoginVo loginVo = new LoginVo();
                loginVo.setLoginName("0");
                loginVo.setMerchantId(merchantId);

                Result<List<OrderInfoVo>> orderListResult = orderService.getOrderInfoList(sourceOrderIds, merchantId);
                if (!orderListResult.isSuccess() || CollectionUtils.isEmpty(orderListResult.getModel())) {
                    return ApiResultUtil.successResult(null);
                }

                List<Long> orderIds = orderListResult.getModel().stream().map(OrderInfoVo::getId)
                        .collect(Collectors.toList());

                OrderInfoParam orderInfoParam = new OrderInfoParam();
                orderInfoParam.setIdList(orderIds);
                orderInfoParam.setMerchantId(merchantId);
                orderInfoParam.setNeedCount(false);
                orderInfoParam.setPageIndex(1);
                orderInfoParam.setPageSize(10000);

                Result<OrderInfoListVo> result = orderSlaveService.getOrderList(orderInfoParam, loginVo);
                if (result.getModel() != null && CollectionUtils.isNotEmpty(result.getModel().getCurrentList())) {
                    if (orderInfoParam.getOrderType() == null || orderInfoParam.getOrderType() == 0) {
                        Result<Map<Long, OrderFeeVo>> mapResult = orderService
                                .getOrderExcelList(result.getModel().getCurrentList());
                        for (OrderInfoVo vo : result.getModel().getCurrentList()) {
                            OrderExcelBean bean = new OrderExcelBean();
                            buildOrderExcelBean(vo, bean);
                            if (vo.getActualPickupDate() != null) {
                                bean.setActualPickupDate(DateUtil.getFormatDateStr(new Date(vo.getActualPickupDate()),
                                        DateUtil.yyyyMMddHHmmss));
                            }
                            if (vo.getActualReturnDate() != null) {
                                bean.setActualReturnDate(DateUtil.getFormatDateStr(new Date(vo.getActualReturnDate()),
                                        DateUtil.yyyyMMddHHmmss));
                            }
                            OrderFeeVo orderFeeVo = mapResult.getModel().get(vo.getId());
                            if (orderFeeVo != null) {
                                bean.setSettlementStatus(FinanceEnum.ReconciliationSettlementStatusEnum
                                        .getName(orderFeeVo.getSettlementStatus()));
                                BeanUtils.copyProperties(orderFeeVo, bean);
                            }
                            if (vo.getActualPickupDate() != null && vo.getActualReturnDate() != null) {
                                bean.setActualRentalPeriod(
                                        DateUtil.getDatePoorHours(vo.getActualPickupDate(), vo.getActualReturnDate()));
                            }
                            if (vo.getIsReptile() != null && vo.getIsReptile().intValue() == 1
                                    && StringUtils.isNotBlank(bean.getOrderSource())) {
                                bean.setOrderSource(bean.getOrderSource() + "同步");
                            }
                            if (vo.getCardOrderExt() != null) {
                                if (Objects.equals("1", vo.getCardOrderExt().getCardTag())) {
                                    bean.setCardTag("次卡");
                                } else if (Objects.equals("2", vo.getCardOrderExt().getCardTag())) {
                                    bean.setCardTag("天卡");
                                } else if (Objects.equals("3", vo.getCardOrderExt().getCardTag())) {
                                    bean.setCardTag("随心租卡");
                                }
                                bean.setCardOrderId(vo.getCardOrderExt().getCardOrderId());
                                bean.setCardId(vo.getCardOrderExt().getCardId());
                                bean.setCardTradeNo(vo.getCardOrderExt().getCardTradeNo());
                                bean.setBasicRentFee(vo.getCardOrderExt().getBasicRentFee());
                                bean.setBasicServiceFee(vo.getCardOrderExt().getBasicServiceFee());
                                bean.setPoundageFee(vo.getCardOrderExt().getPoundageFee());
                            }
                            list.add(bean);
                        }
                    } else {
                        for (OrderInfoVo vo : result.getModel().getCurrentList()) {
                            LongOrderExcelBean bean = new LongOrderExcelBean();
                            buildLongOrderExcelBean(vo, bean);
                            bean.setPayAmount(bean.getPayAmount() / 100);
                            bean.setReceivableAmount(bean.getReceivableAmount() / 100);
                            list.add(bean);
                        }
                    }
                }
                String name = "订单列表";
                EasyExcelUtils.downLoad(response, list, OrderExcelBean.class, name);
            }
            // else if (type == 2) {
            // OrderFeizhuDiscountExcelListener orderExcelListener = new
            // OrderFeizhuDiscountExcelListener();
            // EasyExcel.read(file.getInputStream(),
            // OrderFeizhuDiscountImportExcelBean.class,
            // orderExcelListener).sheet().doRead();
            // List<OrderFeizhuDiscountImportExcelBean> importList =
            // orderExcelListener.getList();
            // log.info("导入订单excel脚本, excel数据={}", JSON.toJSONString(importList));
            // if (CollectionUtils.isEmpty(importList)) {
            // return ApiResultUtil.successResult(null);
            // }
            // }

            return ApiResultUtil.successResult(null);

        } catch (Exception e) {
            log.error("导入订单excel脚本, 异常", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "滴滴解冻脚本")
    @PostMapping("/v1/didi/deposit_thaw")
    public ResultMap depositThaw(@RequestParam("merchantId") @ApiParam("商家ID") Long merchantId,
            HttpServletRequest request, HttpServletResponse response) {
        orderDepositService.depositThaw(merchantId);
        return ApiResultUtil.successResult(null);
    }

    @ApiOperation(value = "滴滴违章解冻脚本")
    @PostMapping("/v1/didi/illegal_deposit_thaw")
    public ResultMap illegalDepositThaw(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            HttpServletRequest request, HttpServletResponse response) {
        orderDepositService.illegalDepositThaw(orderId);
        return ApiResultUtil.successResult(null);
    }

    public static <T> List<List<T>> groupBySize(List<T> list, int groupSize) {
        if (CollectionUtils.isEmpty(list)) {
            return new ArrayList<>();
        }
        if (groupSize < 1 || groupSize > list.size()) {
            groupSize = list.size();
        }
        List<List<T>> rsList = new ArrayList<List<T>>();
        for (int i = 0; i < list.size();) {
            List<T> l = list.subList(i, Math.min(i + groupSize, list.size()));
            if (l != null && l.size() > 0) {
                List<T> nl = new ArrayList<>();
                for (T t : l) {
                    nl.add(t);
                }
                rsList.add(nl);
            }
            i = i + groupSize;
        }
        return rsList;
    }

    /**
     * 订单页面查询 etc费用 和 状态
     */
    @ApiOperation(value = "订单页面查询 etc费用 和 状态")
    @PostMapping("/v1/etcOrderDetails")
    public ResultMap<List<OrderCostDetailsVo>> etcOrderDetails(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            List<OrderCostDetailsVo> voList = etcDeviceService.etcOrderDetails(loginVo.getMerchantId(), orderId);
            return ApiResultUtil.successResult(voList);
        } catch (Exception e) {
            log.error("OrderController etcOrderDetails", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * 取消订单 and 退款接口
     */
    @ApiOperation(value = "取消etc订单")
    @PostMapping("/v1/cancelEtcOrder")
    public ResultMap<List<EtcOrderChargeVo>> cancelEtcOrder(@RequestParam("id") @ApiParam("订单ID") Long id,
            HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            EtcOrderChargeVo vo = etcDeviceService.obtainOrderChargeInfo(id);

            if (EtcConstantsEnum.EtcOrderStatusEnum.canceled_order.getDesc().equals(vo.getOrderStatus()) ||
                    EtcConstantsEnum.EtcOrderStatusEnum.ended_oder.getDesc().equals(vo.getOrderStatus())) {
                throw new BizException("订单已取消");
            }
            Boolean aBoolean = etcDeviceService.cancelEtcOrder(loginVo.getMerchantId(), id);
            if (aBoolean) {
                etcDeviceService.aSynPushETCInfoToTrip(vo.getEtcOrder(), vo.getVehicleId());
            }
            return ApiResultUtil.successResult(aBoolean);
        } catch (Exception e) {
            log.error("OrderController cancelEtcOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    /**
     * didi订单详细
     */
    @ApiOperation(value = "didi订单详细")
    @PostMapping("/v1/didi_order_detail")
    public ResultMap<String> didiOrderDetail(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
            HttpServletRequest request, HttpServletResponse httpResource) {
        try {

            Result<String> result = orderDepositService.getDidiOrderDetail(orderId);
            return ApiResultUtil.successResult(JSON.toJSONString(result));
        } catch (Exception e) {
            log.error("OrderController didiOrderDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "临时统计订单数据")
    @PostMapping("/v1/count_over_order")
    public ResultMap<String> fixOrderSnapshot(HttpServletRequest request) {
        try {
            Result<String> result = orderFixDataService.countOverTimeOriginOrder();
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController fixData", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "补偿财务概览数据")
    @PostMapping("/v3/finance_report/history")
    public ResultMap<Integer> fixHistoryFinanceReport(@RequestBody IOrderFixDataService.FixHistoryParam param,
            HttpServletRequest request) {
        try {
            tokenService.getUserByRequest(request);
            Result<Integer> result = orderFixDataService.fixHistoryFinanceReport(param);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController fixHistoryFinanceReport", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "找到悟空金额错误订单")
    @PostMapping("/v3/find_wukongErrorAmountOrderIds")
    public ResultMap<Integer> findWukongErrorAmountOrderIds(
            @RequestParam("merchantId") @ApiParam("订单ID") Long merchantId,
            @RequestParam("start") @ApiParam("开始时间") Long start,
            @RequestParam("end") @ApiParam("结束时间") Long end,
            HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            tokenService.getUserByRequest(request);
            Result<List<Long>> result = orderFixDataService.findWukongErrorAmountOrderIds(merchantId, start, end);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController findWukongErrorAmountOrderIds", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "修复同程重复取还车订单数据")
    @PostMapping("/v3/fixTcOrderAmount")
    public ResultMap<Integer> fixTcOrderAmount(@RequestParam("orderIdListStr") @ApiParam("订单ID") String orderIdListStr,
            HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            tokenService.getUserByRequest(request);
            List<String> orderIdList = new ArrayList<>();
            if (StringUtils.isNotEmpty(orderIdListStr)) {
                String[] orderIdArray = orderIdListStr.split(",");
                Collections.addAll(orderIdList, orderIdArray);
            }

            orderFixDataService.fixTcOrderAmount(orderIdList);
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController findWukongErrorAmountOrderIds", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "修复携程订单详细押金数据")
    @PostMapping("/v3/fixXcOrderDetail")
    public ResultMap<Integer> fixXcOrderDetail(@RequestParam("merchantId") @ApiParam("商家ID") Long merchantId,
                                               HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            tokenService.getUserByRequest(request);
            orderFixDataService.fixXcOrderDetail(merchantId);
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController fixXcOrderDetail", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @PostMapping("/v3/fixOrderDiscount")
    public ResultMap<Integer> fixOrderDiscount(@RequestBody List<String> sourceOrderIds,
                                               HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            orderFixDataService.fixOrderDiscount(sourceOrderIds);
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController fixOrderDiscount", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @PostMapping("/v3/fixWukongReturnOrder")
    public ResultMap<Integer> fixWukongReturnOrder(@RequestParam("orderId") @ApiParam("订单ID") Long orderId,
                                               @RequestParam("data") @ApiParam("还车数据") String data,
                                               HttpServletRequest request, HttpServletResponse httpResource) {
        try {
            orderFixDataService.fixWukongReturnOrder(orderId, data);
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController fixWukongReturnOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "悟空续租")
    @PostMapping("/v3/wukong_rerent")
    public ResultMap<Integer> wukongRerent(HttpServletRequest request,
            @RequestBody ThirdRerentOrderDTO thirdRerentOrderDTO) {
        try {
            tokenService.getUserByRequest(request);
            thirdOrderService.createRerentOrderV2(thirdRerentOrderDTO);
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController wukongRerent", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "强制修改rap订单状态")
    @PostMapping("/v1/reptile_order/upd_status")
    @WithSpan("手动更新RPA订单状态")
    public ResultMap<Integer> updReptileOrderStatus(@RequestParam("orderId") Long orderId,
            @RequestParam("orderStatus") Byte orderStatus,
            HttpServletRequest request) {
        try {
            SpanEnhancer.of(Span.current()).withLong("订单号", orderId).withByte("订单状态", orderStatus);
            LoginVo user = tokenService.getUserByRequest(request);
            Result<Integer> result = thirdOrderService.updReptileOrderStatus(orderId, user.getMerchantId(),
                    orderStatus);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController updReptileOrderStatus", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "查询虚拟小号")
    @PostMapping("/v1/virtual-mobile")
    @WithSpan("手动更新RPA订单状态")
    public ResultMap<String> updReptileOrderStatus(@RequestParam("orderId") Long orderId,
                                                    HttpServletRequest request) {
        try {

            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController updReptileOrderStatus", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "手动同步RPA订单")
    @PostMapping("/v1/reptile_order/sync")
    @WithSpan("手动同步RPA订单")
    public ResultMap<Integer> syncReptileOrder(HttpServletRequest request,
            @RequestParam("orderId") Long orderId) {
        try {
            SpanEnhancer.of(Span.current()).withLong("订单号", orderId);
            LoginVo user = tokenService.getUserByRequest(request);
            Result<Integer> result = thirdOrderService.syncReptileOrder(orderId, user.getMerchantId());
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(1);
        } catch (Exception e) {
            log.error("OrderController updReptileOrderStatus", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单退款")
    @PostMapping("/v1/refund_order")
    public ResultMap<Boolean> refundOrder(@RequestParam Long orderId, @RequestParam(required = false) Long amount,
            @RequestParam(required = false) Long deductionAmount, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.refundOrder(orderId, loginVo, amount, deductionAmount);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController fixHistoryFinanceReport", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单解冻押金")
    @PostMapping("/v1/unfreeze/deposit")
    public ResultMap<Boolean> unfreezeOrderDeposit(@RequestParam Long orderId,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.unfreezeOrderDeposit(orderId, loginVo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController unfreezeOrderDeposit", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "订单押金退款")
    @PostMapping("/v1/deposit/refund")
    public ResultMap<Boolean> depositRefund(@RequestParam Long orderId, @RequestParam Long refundAmount,
            @RequestParam String payNo,
            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.depositRefund(orderId, refundAmount, loginVo, payNo);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController depositRefund", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "创建三方订单脚本")
    @PostMapping("/v1/third/create")
    public ResultMap<Long> createThirdOrder(@RequestBody @ApiParam("订单信息") ThirdOrderDTO thirdOrderDTO,
                                            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
//            ThirdOrderDTO thirdOrderDTO = JSON.parseObject(order, ThirdOrderDTO.class);
            Result<Long> result = thirdOrderService.createOrder(thirdOrderDTO);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController createThirdOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "获取悟空支付设置")
    @PostMapping("/v1/wukong/pay_setting")
    public ResultMap<List<WokongPaySettingDTO>> getWukongPaySetting(@RequestParam Long orderId, @RequestParam Long merchantId,
                                                                 HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
//            ThirdOrderDTO thirdOrderDTO = JSON.parseObject(order, ThirdOrderDTO.class);
            Result<List<WokongPaySettingDTO>> result = orderService.getWukongPaySetting(merchantId, orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getWukongPaySetting", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "悟空补款")
    @PostMapping("/v1/wukong/add_pay")
    public ResultMap<List<WukongAddPayRespDTO>> wukongAddPay(@RequestBody @ApiParam("补款信息") WukongAddPayDTO wukongAddPayDTO,
                                                             HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
//            ThirdOrderDTO thirdOrderDTO = JSON.parseObject(order, ThirdOrderDTO.class);
            Result<WukongAddPayRespDTO> result = orderService.wukongAddPay(wukongAddPayDTO);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController wukongAddPay", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "悟空取消补款")
    @PostMapping("/v1/wukong/cancel_pay")
    public ResultMap<Boolean> wukongCancelPay(@RequestBody @ApiParam("取消补款信息") WukongPayCancelDTO wukongPayCancelDTO,
                                                             HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
//            ThirdOrderDTO thirdOrderDTO = JSON.parseObject(order, ThirdOrderDTO.class);
            Result<Boolean> result = orderService.wukongCancelPay(wukongPayCancelDTO);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController wukongCancelPay", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "触发悟空保险加购事件")
    @PostMapping("/v1/wukong/added/event")
    public ResultMap<Boolean> wukongAddedEevent(@RequestParam Long orderId,
                                                @RequestParam String itemCode,
                                              HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Boolean> result = orderService.wukongAddedEevent(orderId, itemCode);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController wukongAddedEevent", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @ApiOperation(value = "触发悟空保险加购事件")
    @PostMapping("/v1/pickup/check")
    public ResultMap<WukongPickupCheckDTO> pickUpCheck(@RequestParam Long orderId, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<WukongPickupCheckDTO> result = orderService.pickUpCheck(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController pickUpCheck", e);
            return ApiResultUtil.failResult(e);
        }
    }
    /**
     * todo 本地联调代码
     */
    @ApiOperation(value = "发起履约变更")
    @PostMapping("/v1/modify_order")
    public ResultMap modifyOrder(@RequestBody ModifyOrderVO vo,
                                                               HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> integerResult = thirdOrderService.modifyOrder(vo);
            return ApiResultUtil.successResult(integerResult.getModel());
        } catch (Exception e) {
            log.error("OrderController etcOrderDetails", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "催促改单")
    @PostMapping("/v1/urge_modify_order")
    public ResultMap urgeConfirmModifyOrder(@RequestParam Long orderId,
                                 HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> integerResult = orderService.urgeConfirmModifyOrder(orderId);
            return ApiResultUtil.successResult(integerResult.getModel());
        } catch (Exception e) {
            log.error("OrderController urgeConfirmModifyOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消改单")
    @PostMapping("/v1/cancel_modify_order")
    public ResultMap urgeConfirmModifyOrder(@RequestParam Long orderId,
                                            @RequestParam Long modifyId,
                                            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> integerResult = orderService.canOrderModify(orderId, modifyId);
            return ApiResultUtil.successResult(integerResult.getModel());
        } catch (Exception e) {
            log.error("OrderController urgeConfirmModifyOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "取消改单")
    @PostMapping("/v1/query_modify_order")
    public ResultMap queryOrderModify(@RequestParam Long orderId,
                                            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result<Integer> integerResult = orderService.queryOrderModify(orderId);
            return ApiResultUtil.successResult(integerResult.getModel());
        } catch (Exception e) {
            log.error("OrderController urgeConfirmModifyOrder", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @ApiOperation(value = "获取订单驾驶员虚拟号码")
    @PostMapping("/v1/virtual_phone/get")
    public ResultMap getOrderVirtualPhone(@RequestParam Long orderId, HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result result = orderService.getOrderVirtualPhone(orderId);
            if (ResultUtil.isResultNotSuccess(result)) {
                return ApiResultUtil.failResult(result.getMessage());
            }
            return ApiResultUtil.successResult(result.getModel());
        } catch (Exception e) {
            log.error("OrderController getOrderVirtualPhone", e);
            return ApiResultUtil.failResult(e);
        }
    }


    @PostMapping("/v1/query_order_tag")
    public ResultMap queryOrderTag(@RequestParam Long orderId,
                            HttpServletRequest request) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(request);
            Result integerResult = thirdOrderService.queryOrderTag(orderId);
            return ApiResultUtil.successResult(integerResult.getModel());
        } catch (Exception e) {
            log.error("OrderController doTest", e);
            return ApiResultUtil.failResult(e);
        }
    }

    @PostMapping("/check_vehicle_conflict")
    @ApiOperation("检查车辆改排后库存冲突")
    public ResultMap<VehicleConflictCheckResponse> checkVehicleConflict(@RequestBody @Valid VehicleConflictCheckRequest request,
                                                                        HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            // 调用服务方法
            Result<VehicleConflictCheckResponse> result = orderService.checkVehicleConflict(request, loginVo.getMerchantId());

            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
        } catch (Exception e) {
            log.error("检查车辆改排后库存冲突异常，请求：{}", request, e);
            return ApiResultUtil.failResult(e);
        }
    }
}
