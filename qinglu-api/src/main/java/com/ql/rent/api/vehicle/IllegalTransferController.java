package com.ql.rent.api.vehicle;

import com.ql.rent.common.ITokenService;
import com.ql.rent.param.trade.SubmitContractRequest;
import com.ql.rent.param.trade.TransferContractStatusQuery;
import com.ql.rent.param.trade.VehicleConflictCheckRequest;
import com.ql.rent.service.trade.ITransferContractStatusService;
import com.ql.rent.service.vehicle.IVehicleInfoService;
import com.ql.rent.share.result.ApiResultUtil;
import com.ql.rent.share.result.Result;
import com.ql.rent.share.result.ResultMap;
import com.ql.rent.share.result.ResultUtil;
import com.ql.rent.vo.LoginVo;
import com.ql.rent.vo.PageListVo;
import com.ql.rent.vo.trade.SubmitContractResponse;
import com.ql.rent.vo.trade.TransferContractStatusVo;
import com.ql.rent.vo.trade.VehicleConflictCheckResponse;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

/**
 * 违章转移相关接口
 * <AUTHOR>
 */
@RestController
@RequestMapping("/illegal_transfer/v1")
@Api(tags = "违章转移管理")
@Slf4j
public class IllegalTransferController {

    @Resource
    private ITransferContractStatusService transferContractStatusService;
    
    @Resource
    private ITokenService tokenService;;

    @PostMapping("/contract/submit")
    @ApiOperation("手动上报合同")
    public ResultMap<SubmitContractResponse> submitContract(@RequestBody @Valid SubmitContractRequest request,
                                                            HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);

            // 调用新的手动上报方法
            Result<SubmitContractResponse> result = transferContractStatusService.manualSubmitContract(
                request, 
                loginVo.getMerchantId(), 
                loginVo.getUserId()
            );
            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());
            
        } catch (Exception e) {
            log.error("手动上报合同异常，请求：{}", request, e);
            return ApiResultUtil.failResult(e);
        }
    }

    @PostMapping("/contract/list")
    @ApiOperation("查询合同列表")
    public ResultMap<PageListVo<TransferContractStatusVo>> getContractList(@RequestBody TransferContractStatusQuery query,
                                                                           HttpServletRequest httpServletRequest) {
        try {
            LoginVo loginVo = tokenService.getUserByRequest(httpServletRequest);
            // 设置商家ID
            query.setMerchantId(loginVo.getMerchantId());
            
            // 调用服务方法
            Result<PageListVo<TransferContractStatusVo>> result = transferContractStatusService.getContractList(query);

            if (ResultUtil.isResultSuccess(result)) {
                return ApiResultUtil.successResult(result.getModel());
            }
            return ApiResultUtil.failResult(result.getMessage());

        } catch (Exception e) {
            log.error("查询合同列表异常，请求：{}", query, e);
            return ApiResultUtil.failResult(e);
        }
    }

}
